<?php

namespace App\Modules\Pay\Services;

use App\Library\BaseService;
use App\Library\Enums\PayEnums;
use App\Library\Exception\BusinessException;
use App\Library\FlashPayHelper;
use App\Library\OssHelper;
use App\Library\PGPUtil;
use App\Library\Validation\ValidationException;
use App\Models\oa\PaymentFlashPayConfigModel;
use App\Models\oa\PaymentOnlinePayModel;
use App\Modules\Pay\Models\Payment;
use App\Modules\Pay\Models\PaymentFlashPaySftpLog;
use App\Modules\Pay\Models\PaymentPay;
use App\Modules\Third\Services\FlashPayService;
use Exception;
use GuzzleHttp\Exception\GuzzleException;


/**
 * FlashPay SFTP支付服务类
 * @description: 处理FlashPay SFTP方式的支付文件生成、加密和上传逻辑
 * @author: AI
 * @date: 2025-08-12
 */
class FlashPaySftpService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return FlashPaySftpService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 处理FlashPay SFTP支付流程
     * @description: 查询待支付数据，按公司分组处理，生成Excel文件，加密并上传到SFTP
     * @param $date_at
     * @param $cost_company_id
     * @return bool 处理结果
     * @throws BusinessException|ValidationException
     */
    public function processFlashPaySftp($date_at, $pay_bank_account): bool
    {
        $result = [];
        try {
            // 查询flash_pay_method为2且状态为待支付的数据
            $payment_list = $this->getFlashPayPendingPayments($date_at, $pay_bank_account);

            if (empty($payment_list)) {
                echo 'payment flash pay sftp no data need handle' . PHP_EOL;
                $this->logger->info('payment flash pay sftp no data need handle');
                return true;
            }
            $publicKeyPathConfig = env('flash_pay_sftp_public_key_path');
            if (empty($publicKeyPathConfig)) {
                throw new ValidationException('FlashPay公钥地址未配置');
            }
            // 按费用所属公司分组
            $grouped_payments = $this->groupPaymentsByCompany($payment_list);
            $oss_res          = OssHelper::downloadFileHcm($publicKeyPathConfig, 600);
            if (empty($oss_res['file_url'])) {
                throw new ValidationException('oss下载FlashPay公钥失败');
            }
            $publicKeyPath = sys_get_temp_dir() . '/flash_pay_public_key.gpg';
            if (file_put_contents($publicKeyPath, file_get_contents($oss_res['file_url'])) === false) {
                throw new ValidationException('下载FlashPay公钥到本地失败');
            }

            // 处理每个公司的支付数据
            foreach ($grouped_payments as $pay_bank_account => $payment) {
                $company_result = $this->processCompanyPayments($date_at,$pay_bank_account, $publicKeyPath, $payment);
                $result['data'][$pay_bank_account] = $company_result;
            }
            $this->logger->info(['FlashPay SFTP处理结果' => $result]);
            return true;
        } catch (Exception $e) {
            FlashPayHelper::sendNotice('FlashPay SFTP 支付异常: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取FlashPay待支付数据
     * @description: 查询flash_pay_method为2且状态为待支付的支付记录
     * @return array 支付记录数组
     */
    private function getFlashPayPendingPayments($date_at, $pay_bank_account = ''): array
    {
        $condition = 'flash_pay_method = :flash_pay_method: and pay_status = :pay_status: and is_online_pay = :is_online_pay: 
        and out_send_status = :out_send_status: and planned_pay_date <= :planned_pay_date:';
        $bind      = [
            'flash_pay_method' => PayEnums::PAYMENT_MODULE_FLASH_PAY_METHOD_SFTP, // SFTP方式
            'is_online_pay'    => PayEnums::IS_ONLINE_PAY_YES,
            'pay_status'       => PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING,
            'out_send_status'  => PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_NO,
            'planned_pay_date' => $date_at,
        ];
        if ($pay_bank_account) {
            $condition               .= ' and pay_bank_account = :pay_bank_account:';
            $bind['pay_bank_account'] = $pay_bank_account;
        }
        $payment = Payment::find([
            'conditions' => $condition,
            'bind'       => $bind,
            'columns'    => 'id,no,amount_total_actually,cost_company_id,pay_bank_account,planned_pay_date,flash_pay_method,pay_status,is_online_pay,out_send_status',
        ]);

        return $payment ? $payment->toArray() : [];
    }

    /**
     * 按费用所属公司分组支付数据
     * @description: 将支付记录按cost_company_id进行分组
     * @param array $payment_list 支付记录列表
     * @return array 分组后的支付记录
     */
    private function groupPaymentsByCompany(array $payment_list): array
    {
        $grouped_payments = [];
        foreach ($payment_list as $item) {
            $grouped_payments[$item['pay_bank_account']][] = $item;
        }
        return $grouped_payments;
    }

    /**
     * 处理单个公司的支付数据
     * @description: 处理指定公司的支付数据，包括数据量检查、文件生成、加密和上传
     * @param  string $pay_bank_account
     * @param array $payments 公司支付数据
     * @return array 处理结果
     * @throws Exception
     */
    private function processCompanyPayments($date_at,string $pay_bank_account, $publicKeyPath, array $payments): array
    {
        OssHelper::$is_unlink_file = false;
        $db                        = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $result = [
                'success'          => false,
                'pay_bank_account' => $pay_bank_account,
                'count'            => count($payments),
                'batch_no'         => '',
                'filename'         => '',
            ];

            // 检查数据量是否超过5万条
            if (count($payments) > 50000) {
                throw new ValidationException("支付银行账号: {$pay_bank_account} 的数据量超过5万条，当天不生成数据");
            }
            // 获取SFTP配置
            $flash_pay_config = $this->getFlashPayConfig($pay_bank_account);
            if (empty($flash_pay_config)) {
                throw new ValidationException("支付银行账号: {$pay_bank_account} 未找到FlashPay配置");
            }


            // 生成批次号
            $batch_no           = $this->generateBatchNo($flash_pay_config->flashpay_sftp_shopname ?? '');
            $result['batch_no'] = $batch_no;

            // 先更新支付状态为待反馈，防止并发处理
            foreach ($payments as &$payment) {
                $params                  = ['out_batch_number' => $batch_no];
                $onlinePayModel          = FlashPayService::getInstance()->savePayDb(PayEnums::PAYMENT_MODULE_FLASH_PAY_METHOD_SFTP,$payment['id'], $params);
                $payment['out_trade_no'] = $onlinePayModel->out_trade_no;
            }

            $result['data_list'] = $payments;
            // 生成Excel文件
            $file_result = $this->generateTransferExcel($payments, $batch_no);

            if (!$file_result['success']) {
                throw new ValidationException("Excel文件生成失败: {$file_result['error']}");
            }
            //上传oss留档
            $oss_res                   = OssHelper::uploadFile($file_result['file_path'], 'flashPaySFTP');
            $result['excel_file_path'] = $oss_res['object_url'];
            // PGP加密文件
            $encrypted_file_path           = $this->encryptFile($file_result['file_path'], $publicKeyPath,
                $flash_pay_config);
            $oss_res                       = OssHelper::uploadFile($encrypted_file_path, 'flashPaySFTPEncrypt');
            $result['encrypted_file_path'] = $oss_res['object_url'];
            // 上传加密文件到SFTP
            $upload_result = $this->uploadToSftp($encrypted_file_path, $flash_pay_config);
            if ($upload_result['success']) {
                $result['success']  = true;
                $result['filename'] = $upload_result['filename'];
                $result['message']  = "处理成功，批次号: {$batch_no}，上传文件: {$upload_result['filename']}";
            } else {
                // 上传失败，需要回滚状态
                $rollbackResult    = $this->rollbackPaymentStatus($payments);
                $result['message'] = "SFTP上传失败: {$upload_result['error']} 回滚状态: " . ($rollbackResult ? '成功' : '失败');
            }

            $logModel = new PaymentFlashPaySftpLog();
            $log_data = [
                'action_date'         => $date_at,
                'pay_bank_account'    => $pay_bank_account,
                'out_batch_number'    => $batch_no,
                'excel_file_path'     => $result['excel_file_path']??'',
                'encrypted_file_path' => $result['encrypted_file_path']??'',
            ];
            $logModel->i_create($log_data);

            $db->commit();
            // 清理文件
            $this->cleanupFiles([$file_result['file_path'], $encrypted_file_path]);
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        return $result;
    }

    /**
     * 获取FlashPay配置
     * @description: 根据支付账号获取FlashPay SFTP配置
     * @param string $pay_bank_account 支付账号
     * @return PaymentFlashPayConfigModel|null 配置对象
     */
    private function getFlashPayConfig(string $pay_bank_account)
    {
        return PaymentFlashPayConfigModel::findFirst([
            'conditions' => 'flashpay_sftp_shopname = :flashpay_sftp_shopname:',
            'bind'       => ['flashpay_sftp_shopname' => $pay_bank_account],
        ]);
    }

    /**
     * 获取所有FlashPay配置
     * @param int $cost_company_id
     * @return mixed
     */
    private function getAllFlashPayConfig(int $cost_company_id = 0)
    {
        if ($cost_company_id) {
            $condition = 'cost_company_id = :cost_company_id:';
            $bind      = ['cost_company_id' => $cost_company_id];
        } else {
            $condition = '1 = :test:';
            $bind      = ['test' => 1];
        }

        // 获取所有FlashPay配置
        return PaymentFlashPayConfigModel::find(
            [
                'conditions' => $condition,
                'bind'       => $bind,
            ]
        )->toArray();
    }

    /**
     * 生成批次号
     * @description: 生成FlashPay批次号，规则: FlashPay stfp商户编号_日期_当天的文件数
     * @param string $shop_name SFTP商户编号
     * @return string 批次号
     */
    private function generateBatchNo($shop_name)
    {
        $date = date('Ymd');
        if (RUNTIME == 'dev') {
            $date = date('Ymd') . rand(100, 999);
        }
        return $shop_name . '-' . $date . '-1';
    }

    /**
     * 生成FlashPay转账Excel文件
     * @description: 根据FlashPay官方格式规范生成转账Excel文件
     * @param array $payments 支付数据数组
     * @param string $batch_no 外部批次号
     * @return array 返回生成结果
     * @author: AI
     * @date: 2025-08-13
     */
    private function generateTransferExcel(array $payments, string $batch_no): array
    {
        //c、加密方式为 PGP 加密，加密后文件名称：transfer_${批次号}_${yyyyMMddHHmmss}.xlsx.gpg
        $dateTime = date('YmdHis');
        $fileName = 'transfer_' . $batch_no . '_' . $dateTime . '.xlsx';
        $filePath = sys_get_temp_dir() . '/' . $fileName;

        $excel      = new \Vtiful\Kernel\Excel(['path' => sys_get_temp_dir()]);
        $fileObject = $excel->fileName($fileName);

        // 根据FlashPay官方格式设置表头
        $header = [
            'Out Batch Number',       // 外部批次号 - 必填
            'Out Order Number',       // 外部订单号 - 必填
            'Transfer Amount',        // 代付金额 - 必填
            'Payee Account Name',     // 收款人开户名 - 必填
            'Payee Account Number',   // 收款人银行账号 - 必填
            'Payee Bank',             // 收款银行名 - 非必填
            'Payee Bank Code',        // 收款银行代码 - 必填
            'Payee Phone Number',     // 收款人手机号 - 非必填
            'Payee Email',            // 收款人邮箱 - 非必填
            'Transfer Instructions',  // 说明 - 非必填
            'Notes',                  // 备注 - 非必填
            'outBizNo',               // 业务单号 - 非必填
        ];
        $fileObject->header($header);

        // 填充数据
        $data = [];
        foreach ($payments as $payment) {
            // 外部订单号：OA单号+随机数
            $outOrderNumber = $payment['out_trade_no'];
            // 格式化转账金额
            $transferAmount = intval(bcmul($payment['amount_total_actually'], 100));
            //在线支付的单据，单据的收款信息的银行都是一样的，所以获取单据下的一笔支付信息即可
            $payment_pay = PaymentPay::findFirst([
                'columns'    => 'bank_name,bank_account,bank_account_name',
                'conditions' => 'payment_id = :payment_id:',
                'bind'       => ['payment_id' => $payment['id']],
            ]);
            if (empty($payment_pay)) {
                $this->logger->error(['payment_pay not found' => $payment['id'], 'batch_no' => $batch_no]);
                continue;
            }
            // 获取收款银行代码
            $payeeBankCode = $this->getPayeeBankCode($payment_pay->bank_name);
            if (empty($payeeBankCode)) {
                $this->logger->error(['getPayeeBankCode not found' => $payment['id'], 'batch_no' => $batch_no]);
                continue;
            }
            $outBizNo = $payment['no'];
            $data[]   = [
                $batch_no,                        // Out Batch Number
                $outOrderNumber,                  // Out Order Number
                $transferAmount,                  // Transfer Amount
                $payment_pay->bank_account_name,  // Payee Account Name
                $payment_pay->bank_account,       // Payee Account Number
                '',                               // Payee Bank
                $payeeBankCode,                   // Payee Bank Code
                '',                               // Payee Phone Number
                '',                               // Payee Email
                'pay ' . $payment['no'],          // Transfer Instructions
                $payment['no'],                   // Notes
                $outBizNo,                        // outBizNo
            ];
        }

        $fileObject->data($data);
        $fileObject->output();

        return [
            'success'   => true,
            'file_path' => $filePath,
            'error'     => '',
        ];
    }

    /**
     * 获取收款银行代码
     * @description: 获取符合FlashPay规范的银行代码
     * @param string $bank_name 收款银行名称
     * @return string 银行代码
     * @author: AI
     * @date: 2025-08-13
     */
    private function getPayeeBankCode($bank_name)
    {
        static $flash_pay_bank;
        if (empty($flash_pay_bank)) {
            $flash_pay_bank = PayService::getInstance()->getFlashPayBank();
        }
        return $flash_pay_bank[$bank_name];
    }

    /**
     * 文件进行PGP加密
     * @param string $file_path 原始文件路径
     * @param PaymentFlashPayConfigModel $config FlashPay配置
     * @return string 加密后文件路径
     * @throws Exception
     * @author: AI
     * @date: 2025-01-15
     */
    private function encryptFile(string $file_path, $publicKeyPath, PaymentFlashPayConfigModel $config): string
    {
        $encrypted_file_path = $file_path . '.gpg';
        try {
            // 检查原始文件是否存在
            if (!file_exists($file_path)) {
                throw new Exception('原始文件不存在: ' . $file_path);
            }
            // 检查公钥是否存在
            if (!file_exists($publicKeyPath)) {
                throw new Exception('公钥不存在: ' . $publicKeyPath);
            }
            PGPUtil::encryptFile($encrypted_file_path, $file_path, $publicKeyPath);
            echo '商户[' . $config->flashpay_sftp_shopname .']文件PGP加密成功' . PHP_EOL;
            $this->logger->info('文件PGP加密成功: ' . $encrypted_file_path);
            return $encrypted_file_path;
        } catch (Exception $e) {
            // 清理可能生成的加密文件
            if (file_exists($encrypted_file_path)) {
                unlink($encrypted_file_path);
            }
            throw $e;
        }
    }


    /**
     * 确保远程目录存在，如果不存在则递归创建
     * @param resource $sftp SFTP连接资源
     * @param string $remote_dir 远程目录路径
     * @return bool
     */
    private function ensureRemoteDirectory($sftp, $remote_dir)
    {
        // 移除末尾的斜杠
        $remote_dir = rtrim($remote_dir, '/');

        // 检查目录是否存在
        if (\ssh2_sftp_stat($sftp, $remote_dir)) {
            return true;
        }

        // 递归创建父目录
        $parent_dir = dirname($remote_dir);
        if ($parent_dir !== $remote_dir && $parent_dir !== '.' && $parent_dir !== '/') {
            $this->ensureRemoteDirectory($sftp, $parent_dir);
        }

        // 创建当前目录
        return \ssh2_sftp_mkdir($sftp, $remote_dir, 0755);
    }

    /**
     * 上传文件到SFTP
     * @description: 将加密文件上传到FlashPay SFTP服务器
     * @param string $file_path 文件路径
     * @param PaymentFlashPayConfigModel $config FlashPay配置
     * @return array 上传结果
     * @throws Exception
     */
    private function uploadToSftp(string $file_path, PaymentFlashPayConfigModel $config): array
    {
        try {
            // 从配置表获取SFTP连接信息
            $host = env('flash_pay_sftp_ip');
            $port = env('flash_pay_sftp_port', 22);

            if (empty($host)) {
                throw new Exception('SFTP服务器地址未配置');
            }

            $username = $config->flashpay_sftp_username ?? '';
            $password = $config->flashpay_sftp_password ?? '';

            if (empty($username) || empty($password)) {
                throw new Exception('SFTP用户名或密码未配置');
            }

            // 检查SSH2扩展是否可用
            if (!function_exists('ssh2_connect')) {
                throw new Exception('SSH2扩展未安装，无法使用SFTP功能');
            }

            // 创建SSH连接
            $connection = \ssh2_connect($host, $port);
            if (!$connection) {
                throw new Exception('无法连接到SFTP服务器: ' . $host . ':' . $port);
            }

            // 认证
            if (!\ssh2_auth_password($connection, $username, $password)) {
                throw new Exception('SFTP认证失败，用户名: ' . $username);
            }

            // 创建SFTP连接
            $sftp = \ssh2_sftp($connection);
            if (!$sftp) {
                throw new Exception('创建SFTP连接失败');
            }

            // 上传目录 - 根据商户编号动态设置
            $remote_dir = '/in/trade/transfer/h2h/latest/';
            //$remote_dir = '/out/trade/result/transfer/h2h/acceptance/latest/';
            $filename = basename($file_path);

            // 获取用户主目录的绝对路径
            $home_dir = \ssh2_sftp_realpath($sftp, '.');
            if (!$home_dir) {
                // 如果无法获取当前目录，尝试获取根目录
                $home_dir = \ssh2_sftp_realpath($sftp, '/');
                if (!$home_dir) {
                    $home_dir = '';
                }
            }

            // 构建完整的远程路径
            $full_remote_dir = rtrim($home_dir, '/') . $remote_dir;
            $remote_file     = $full_remote_dir . $filename;

            // 检查并创建远程目录
            $this->ensureRemoteDirectory($sftp, $full_remote_dir);

            // 使用SFTP方式上传文件，而不是SCP
            $local_file_handle = fopen($file_path, 'r');
            if (!$local_file_handle) {
                throw new Exception('无法打开本地文件: ' . $file_path);
            }

            $remote_file_handle = fopen('ssh2.sftp://' . intval($sftp) . $remote_file, 'w');
            if (!$remote_file_handle) {
                fclose($local_file_handle);
                throw new Exception('无法创建远程文件: ' . $remote_file);
            }

            // 复制文件内容
            $upload_success = stream_copy_to_stream($local_file_handle, $remote_file_handle);

            fclose($local_file_handle);
            fclose($remote_file_handle);

            if (!$upload_success) {
                throw new Exception('文件上传失败: ' . $filename);
            }
            echo '商户[' . $config->flashpay_sftp_shopname  .'] 文件上传成功: ' . $remote_file . PHP_EOL;
            return [
                'success'     => true,
                'filename'    => $filename,
                'remote_path' => $remote_file,
                'error'       => '',
            ];
        } catch (Exception $e) {
            return [
                'success'  => false,
                'filename' => basename($file_path),
                'error'    => $e->getMessage(),
            ];
        }
    }

    /**
     * 回滚支付状态（上传失败时调用）
     * @description: 上传失败时回滚支付状态
     * @param array $payments 支付数据数组
     * @return bool
     * @throws Exception
     */
    private function rollbackPaymentStatus(array $payments)
    {
        return Payment::find([
            'conditions' => 'id in ({ids:array})',
            'bind'       => ['ids' => array_column($payments, 'id')],
        ])->update([
            'out_batch_number' => null,
            'out_send_status'  => PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_NO,
            'pay_status'       => PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING,
            'updated_at'       => gmdate('Y-m-d H:i:s'),
        ]);
    }

    /**
     * 清理临时文件
     * @description: 删除生成的临时文件
     * @param array $file_paths 文件路径数组
     * @return void
     */
    private function cleanupFiles($file_paths)
    {
        if (RUNTIME == 'dev') {
            return;
        }
        foreach ($file_paths as $file_path) {
            if (file_exists($file_path)) {
                unlink($file_path);
            }
        }
    }

    /**
     * 获取SFTP支付结果
     * @description: 定时任务获取SFTP支付结果，按商户号获取对应的支付结果文件
     * @param string $pay_bank_account
     * @return array 处理结果
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function getFlashPaySftpResults(string $pay_bank_account = ''): array
    {
        $result  = [];
        $configs = FlashPayService::getInstance()->getAllFlashPayConfig($pay_bank_account);
        foreach ($configs as $config) {
            if (empty($config['pgp_private_key_path']) || empty($config['pgp_private_key_password'])) {
                continue;
            }
            $config['pgp_private_key_file_path'] = $this->getGpgPrivateKey($config);
            $configResult = $this->processConfigResults($config);
            $result[]     = $configResult;
        }
        $this->logger->info(['FlashPay SFTP结果处理结果' => $result]);
        return $result;
    }


    /**
     * 下载PGP私钥
     * @param $config
     * @return string
     * @throws BusinessException
     * @throws ValidationException
     */
    protected function getGpgPrivateKey($config): string
    {
        $localFile = APP_PATH . '/runtime/' . $config['flashpay_sftp_shopname'] . '_' . get_runtime() . '_private_key.gpg';
        if (!file_exists($localFile)) {
            $oss_res = OssHelper::downloadFileHcm($config['pgp_private_key_path'], 600);
            if (empty($oss_res['file_url'])) {
                throw new ValidationException('HCM获取FlashPay私钥失败:' . $config['cost_company_id']);
            }
            if (file_put_contents($localFile, file_get_contents($oss_res['file_url'])) === false) {
                throw new ValidationException('FlashPay私钥下载本地失败:' . $config['cost_company_id']);
            }
        }
        return $localFile;
    }


    /**
     * 处理单个配置的支付结果
     * @description: 处理单个商户配置的支付结果文件
     * @param array $config FlashPay配置数组
     * @return array 处理结果
     * @throws Exception|GuzzleException
     */
    private function processConfigResults(array $config): array
    {
        $result = [
            'success'                => true,
            'config_id'              => $config['id'],
            'cost_company_id'        => $config['cost_company_id'],
            'flashpay_sftp_shopname' => $config['flashpay_sftp_shopname'],
            'error'                  => '',
        ];

        // 获取未取完结果的批次号
        $pendingBatches = $this->getPendingResultBatches($config['flashpay_sftp_shopname']);

        if (empty($pendingBatches)) {
            $result['error'] = 'No pending batches found';
            return $result;
        }

        // 建立SFTP连接（复用连接避免重复连接）
        [$connection, $sftp] = $this->createSftpConnection($config);

        foreach ($pendingBatches as $batchNo) {
            $result[$batchNo] = $this->downloadAndProcessResultFile($config, $batchNo, $sftp);
        }
        //关闭连接
        ssh2_disconnect($connection);

        return $result;
    }

    /**
     * 获取未取完结果的批次号
     * @description: 查询所有未取完支付结果的批次号
     * @param  $flashpay_sftp_shopname --商户号
     * @return array 批次号数组
     */
    private function getPendingResultBatches($flashpay_sftp_shopname): array
    {
        // 查询状态为待反馈的支付记录的批次号
        $condition = 'pay_bank_account = :pay_bank_account: and is_online_pay = :is_online_pay: and  
        pay_status = :pay_status: and flash_pay_method = :flash_pay_method: 
        AND out_send_status = :out_send_status: AND out_batch_number IS NOT NULL';
        $payments  = Payment::find([
            'conditions' => $condition,
            'bind'       => [
                'is_online_pay'    => PayEnums::IS_ONLINE_PAY_YES,
                'flash_pay_method' => PayEnums::PAYMENT_MODULE_FLASH_PAY_METHOD_SFTP,
                'pay_status'       => PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING,
                'out_send_status'  => PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_WAIT,
                'pay_bank_account' => $flashpay_sftp_shopname,
            ],
            'columns'    => 'DISTINCT(out_batch_number) as out_batch_number',
        ])->toArray();
        if (empty($payments)) {
            return [];
        }
        return array_unique(array_column($payments, 'out_batch_number'));
    }


    /**
     * 获取未取完结果的批次号
     * @param $date_at
     * @return array|void
     */
    public function getPendingResultBatchesByDate($date_at)
    {
        // 查询状态为待反馈的支付记录的批次号
        $condition = 'planned_pay_date <= :planned_pay_date: and is_online_pay = :is_online_pay: and  
        pay_status = :pay_status: and flash_pay_method = :flash_pay_method: 
        AND out_send_status = :out_send_status: AND out_batch_number IS NOT NULL';
        $payments  = Payment::find([
            'conditions' => $condition,
            'bind'       => [
                'is_online_pay'    => PayEnums::IS_ONLINE_PAY_YES,
                'flash_pay_method' => PayEnums::PAYMENT_MODULE_FLASH_PAY_METHOD_SFTP,
                'pay_status'       => PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING,
                'out_send_status'  => PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_WAIT,
                'planned_pay_date'  => $date_at,
            ],
            'columns'    => 'id,no, out_batch_number,planned_pay_date',
        ])->toArray();
        if (empty($payments)) {
            return [];
        }
        return $payments;

    }


    /**
     * 下载并处理支付结果文件
     * @description: 下载、解密并解析支付结果文件
     * @param array $config FlashPay配置数组
     * @param string $batchNo 批次号
     * @param $connection
     * @param $sftp
     * @return array 处理结果
     * @throws BusinessException
     * @throws GuzzleException
     */
    private function downloadAndProcessResultFile(array $config, string $batchNo, $sftp): array
    {
        // 解析Excel文件
        OssHelper::$is_unlink_file = false;
        $result                    = [
            'success'  => true,
            'batch_no' => $batchNo,
            'error'    => '',
        ];

        // 构建结果文件路径
        $remoteDir = '/out/trade/result/transfer/h2h/acceptance/latest/';

        // 使用模糊匹配查找文件
        $regexPattern = sprintf('/^transfer_%s_\d{14}\_result_acceptance_\d{14}\.xlsx\.gpg$/', $batchNo);

        $matchedFiles = $this->findFileByPatternWithConnection($sftp, $remoteDir, $regexPattern);
        if (empty($matchedFiles)) {
            $result['success'] = true;
            $result['error']   = sprintf('未找到批次号 %s 对应的结果文件', $batchNo);
            return $result;
        }
        foreach ($matchedFiles as $matchedFile) {
            // 下载文件
            $localFile = sys_get_temp_dir() . '/' . basename($matchedFile);
            try {
                $this->downloadResultFileWithConnection($sftp, $remoteDir, $matchedFile, $localFile);
            } catch (Exception $e) {
                $this->logger->error('下载结果文件失败: ' . $e->getMessage());
                continue;
            }

            $private_key_path = $config['pgp_private_key_file_path'];
            // 解密文件
            $decryptedFile = str_replace('.gpg', '', $localFile);
            PGPUtil::decryptFile($localFile, $private_key_path, $config['pgp_private_key_password'], $decryptedFile);
            $oss_res                  = OssHelper::uploadFile($decryptedFile, 'flashPaySFTPResult');
            $result['excel_file_url'] = $oss_res['object_url'];
            $parseResult              = $this->parseResultExcel($decryptedFile, $batchNo);
            $result[$batchNo][]         = $parseResult;
            // 清理临时文件
            $this->cleanupFiles([$localFile, $decryptedFile]);
        }

        return $result;
    }

    /**
     * 创建SFTP连接
     * @description: 创建并返回SFTP连接，用于复用连接避免重复连接
     * @param array $config FlashPay配置数组
     * @return array 连接结果，包含success、connection、sftp、error字段
     * @author: AI
     * @date: 2024-01-15 10:30:00
     */
    private function createSftpConnection($config): array
    {
        // 获取SFTP连接信息
        $host = env('flash_pay_sftp_ip');   // SSH服务器地址
        $port = env('flash_pay_sftp_port'); // SSH端口号

        $username = $config['flashpay_sftp_username'];
        $password = $config['flashpay_sftp_password'];

        // 创建SSH连接
        $connection = \ssh2_connect($host, $port);
        if (!$connection) {
            throw new Exception('无法连接到SFTP服务器: ' . $host . ':' . $port);
        }

        // 认证
        if (!\ssh2_auth_password($connection, $username, $password)) {
            throw new Exception('SFTP认证失败，用户名: ' . $username);
        }

        // 创建SFTP连接
        $sftp = \ssh2_sftp($connection);
        if (!$sftp) {
            throw new Exception('创建SFTP连接失败');
        }

        return [$connection, $sftp];
    }

    /**
     * 使用已有SFTP连接查找文件
     * @description: 使用已建立的SFTP连接在远程目录中查找匹配指定模式的文件
     * @param resource $sftp SFTP连接资源
     * @param string $remoteDir 远程目录
     * @param $regexPattern
     * @return array|false 匹配的文件名，未找到返回false
     * @throws Exception
     * @author: AI
     * @date: 2024-01-15 10:30:00
     */
    private function findFileByPatternWithConnection($sftp, $remoteDir, $regexPattern)
    {
        // 列出目录文件
        $handle = opendir("ssh2.sftp://$sftp$remoteDir");
        if (!$handle) {
            throw new Exception('无法打开远程目录: ' . $remoteDir);
        }

        // 将模式转换为正则表达式
        $matchedFiles = [];
        while (($file = readdir($handle)) !== false) {
            if ($file === '.' || $file === '..') {
                continue;
            }

            if (preg_match($regexPattern, $file)) {
                $matchedFiles[] = $file;
            }
        }

        closedir($handle);

        // 如果没有匹配的文件，返回false
        if (empty($matchedFiles)) {
            return false;
        }

        return $matchedFiles;
    }

    /**
     * 使用已有连接下载支付结果文件
     * @description: 使用已建立的SSH连接从SFTP服务器下载支付结果文件
     * @param $sftp
     * @param string $remoteDir 远程目录
     * @param string $fileName 文件名
     * @param string $localFile 本地文件路径
     * @return bool 下载结果
     * @throws Exception
     * @author: AI
     * @date: 2024-01-15 10:30:00
     */
    private function downloadResultFileWithConnection($sftp, $remoteDir, $fileName, $localFile): bool
    {
        // 构建远程文件路径
        $remoteFile = $remoteDir . $fileName;
        if (!copy("ssh2.sftp://{$sftp}{$remoteFile}", $localFile)) {
            throw new Exception('文件下载失败: ' . $fileName);
        }
        return true;
    }

    /**
     * 解析支付结果Excel文件
     * @description: 解析支付结果Excel文件并更新支付状态
     * @param string $excelFile Excel文件路径
     * @param string $batchNo 批次号
     * @return array 解析结果
     * @throws BusinessException
     */
    private function parseResultExcel($excelFile, $batchNo): array
    {
        // 检查文件是否存在
        if (!file_exists($excelFile)) {
            throw new BusinessException('Excel文件不存在: ' . $excelFile);
        }

        // 读取Excel文件
        $excel = new \Vtiful\Kernel\Excel(['path' => dirname($excelFile)]);
        $data  = $excel->openFile(basename($excelFile))->openSheet()->getSheetData();

        if (empty($data)) {
            throw new BusinessException('Excel文件为空或读取失败');
        }

        // 获取表头，确定列位置
        $headers   = array_shift($data);
        $columnMap = $this->mapResultColumns($headers);

        if (empty($columnMap)) {
            throw new BusinessException('Excel表头格式不正确，无法识别必要字段');
        }

        $result = [];
        // 处理每一行数据
        foreach ($data as $row) {
            try {
                $rowResult = $this->processResultRow($row, $columnMap, $batchNo);
            } catch (Exception $e) {
                $this->logger->error(['处理结果文件行失败' => $e->getMessage(), $row, $columnMap, $batchNo]);
                continue;
            }

            $result[] = $rowResult;
        }

        return $result;
    }

    /**
     * 映射结果文件列名
     * @description: 根据表头映射列名到列索引
     * @param array $headers 表头数组
     * @return array 列映射数组
     */
    private function mapResultColumns($headers)
    {
        $columnMap = [];

        // 定义需要的列名映射
        $requiredColumns = [
            'Transaction Batch No'     => 'transaction_batch_no',
            'Transaction Order No'     => 'transaction_order_no',
            'Merchant Batch No'        => 'merchant_batch_no',
            'Merchant Order No'        => 'merchant_order_no',
            'Trade Type'               => 'trade_type',
            'Order Amount'             => 'order_amount',
            'Currency'                 => 'currency',
            'Transaction Status'       => 'transaction_status',
            'Transaction Fail reasons' => 'transaction_fail_reasons',
            'Creation Time'            => 'creation_time',
            'Transfer End Time'        => 'transfer_end_time',
        ];

        foreach ($headers as $index => $header) {
            $header = trim($header);
            if (isset($requiredColumns[$header])) {
                $columnMap[$requiredColumns[$header]] = $index;
            }
        }

        // 检查必要字段是否存在
        $requiredFields = ['merchant_order_no', 'transaction_status', 'transaction_batch_no', 'transaction_order_no'];
        foreach ($requiredFields as $field) {
            if (!isset($columnMap[$field])) {
                $this->logger->error('Excel缺少必要字段: ' . $field);
                return [];
            }
        }

        return $columnMap;
    }

    /**
     * 处理结果文件单行数据
     * @description: 处理支付结果文件的单行数据，更新对应的支付状态
     * @param array $row 行数据
     * @param array $columnMap 列映射
     * @param string $batchNo 批次号
     * @return array 处理结果
     */
    private function processResultRow($row, $columnMap, $batchNo)
    {
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 获取关键字段值
            $merchantOrderNo        = trim($row[$columnMap['merchant_order_no']] ?? '');
            $transactionStatus      = trim($row[$columnMap['transaction_status']] ?? '');
            $transactionBatchNo     = trim($row[$columnMap['transaction_batch_no']] ?? '');
            $transactionOrderNo     = trim($row[$columnMap['transaction_order_no']] ?? '');
            $transactionFailReasons = trim($row[$columnMap['transaction_fail_reasons']] ?? '');
            $transferEndTime        = trim($row[$columnMap['transfer_end_time']] ?? '');
            $creationTime           = trim($row[$columnMap['creation_time']] ?? '');
            // 验证必要字段
            if (empty($merchantOrderNo)) {
                throw new BusinessException('商户订单号为空');
            }

            if (empty($transactionStatus)) {
                throw new Exception('交易状态为空');
            }

            $paymentOnlineModel = PaymentOnlinePayModel::findFirst([
                'conditions' => 'out_trade_no = :out_trade_no: and out_batch_number = :out_batch_number:',
                'bind'       => ['out_trade_no' => $merchantOrderNo, 'out_batch_number' => $batchNo],
            ]);
            if (empty($paymentOnlineModel)) {
                $alertMessage = sprintf('在payment_online_pay表中未找到记录 支付流水号 %s', $merchantOrderNo);
                // 在OA系统中查询不到该交易号，发送飞书预警
                FlashPayHelper::sendNotice($alertMessage);
                throw new Exception($alertMessage);
            }

            // 查询OA系统中的支付记录
            $payment = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $paymentOnlineModel->payment_id],
                'for_update' => true,
            ]);

            if (empty($payment)) {
                // 在OA系统中查询不到该交易号，发送飞书预警
                $alertMessage = sprintf('FlashPay结果处理：在OA系统中查询不到交易号 %s', $merchantOrderNo);
                FlashPayHelper::sendNotice($alertMessage);
                throw new Exception('在OA系统中查询不到交易号: ' . $merchantOrderNo);
            }

            // 根据交易状态更新支付记录
            $updateResult = $this->updatePaymentStatus(
                $payment,
                $paymentOnlineModel,
                $transactionStatus,
                $transactionBatchNo,
                $transactionOrderNo,
                $transactionFailReasons,
                $transferEndTime,
                $creationTime
            );
            $db->commit();
            return $updateResult;
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }

    /**
     * 更新支付状态
     * @description: 根据FlashPay返回的交易状态更新支付记录
     * @param Payment $payment 支付记录
     * @param PaymentOnlinePayModel $paymentOnlineModel
     * @param string $transactionStatus 交易状态
     * @param string $transactionBatchNo 交易批次号
     * @param string $transactionOrderNo 交易订单号
     * @param string $transactionFailReasons 失败原因
     * @param string $transferEndTime
     * @param string $creationTime
     * @return array 更新结果
     * @throws Exception
     */
    private function updatePaymentStatus(
        Payment $payment,
        PaymentOnlinePayModel $paymentOnlineModel,
        string $transactionStatus,
        string $transactionBatchNo,
        string $transactionOrderNo,
        string $transactionFailReasons,
        string $transferEndTime,
        string $creationTime
    ) {
        // 创建SFTP参数对象
        $excelData = [
            'transaction_batch_no'     => $transactionBatchNo,
            'transaction_order_no'     => $transactionOrderNo,
            'transaction_fail_reasons' => $transactionFailReasons,
            'transfer_end_time'        => $transferEndTime,
            'creation_time'            => $creationTime,
        ];
        if ($payment->pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY) {
            $this->logger->warning('payment当前状态为已支付  重复执行 单号:' . $payment->no);
            return $excelData;
        }
        $params    = new FlashPaySftpUpdateParams(
            $payment,
            $paymentOnlineModel,
            $transactionStatus,
            $excelData
        );

        // 使用通用的FlashPay支付状态更新服务
        FlashPayStatusUpdateService::getInstance()->updatePaymentStatus($params);
        return $excelData;
    }


}