<?php
namespace App\Modules\Pay\Services;

use App\Library\BaseController;
use App\Library\Enums;
use App\Library\Enums\WagesEnums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ChequeAccountModel;
use App\Modules\AgencyPayment\Services\AgencyPaymentPayService;
use App\Modules\Cheque\Services\ChequeService;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentUpdateService;
use App\Modules\Pay\Models\Payment;
use App\Modules\Pay\Models\PaymentCheck;
use App\Modules\Pay\Models\PaymentPay;
use App\Modules\Payment\Services\StoreRentingUpdateService;
use App\Modules\Purchase\Services\PaymentService;
use App\Modules\Reimbursement\Services\UpdateService;
use App\Modules\ReserveFund\Services\ApplyService;
use App\Modules\User\Services\UserService;
use App\Modules\Wages\Models\WagesModel;
use App\Modules\Wages\Services\AddService;
use App\Modules\Workflow\Models\WorkflowAuditLogModel;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Loan\Services\UpdateService as LoanUpdateService;
use App\Modules\BankFlow\Services\PayFlowService as BankFlowPayFlowService;
use App\Modules\Salary\Services\UpdateService as SalaryUpdateService;
use App\Modules\Wages\Services\UpdateService as WagesUpdateService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\HrStaffRepository;

class FinalPayService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 是否支付模块支付人
     * @param $staff_info_id
     * @return bool
     * @date 2022/5/16
     */
    public function checkPayer($staff_info_id)
    {
        $payer_arr = EnumsService::getInstance()->getPayModulePayer();
        if (in_array($staff_info_id, $payer_arr)) {
            return true;
        }
        return false;
    }

    /**
     * 列表
     * @param integer $staff_info_id 用户ID
     * @param array $condition 查询条件组
     * @param bool $check_payer 是否鉴权三级支付人
     * @return array
     */
    public function getList($staff_info_id, $condition, $check_payer = true)
    {
        $condition['uid'] = $staff_info_id;
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            if ($check_payer && !$this->checkPayer($staff_info_id)) {
                throw new ValidationException(static::$t->_('pay_method_payer_error'), ErrCode::$VALIDATE_ERROR);
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['p' => Payment::class]);
            $builder = $this->getCondition($builder, $condition);

            $count = (int)$builder->columns('COUNT(DISTINCT(p.id)) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $builder->columns(
                    'out_trade_no,if(p.payment_voucher = \'\', 0, 1) AS is_has_payment_voucher, p.id,p.oa_type,p.no,p.apply_staff_id,p.apply_staff_name,p.cost_company_name,p.apply_date,p.pay_method,p.pay_status,p.amount_total_actually,p.currency,p.out_send_status,p.out_send_at,out_trade_code,p.pay_bank_name,p.pay_bank_account,p.planned_pay_date'
                );
                $builder->limit($page_size, $offset);
                $builder->groupBy('p.id');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items);
            }

            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error('final-pay-getList-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 审核待办统计
     *
     * 没有意见征询
     *
     * @param int $biz_type
     * @param int $user_id
     * @return mixed
     */
    public function getAuditPendingCount(int $biz_type, int $user_id)
    {
        if (empty($user_id) || empty($biz_type)) {
            return 0;
        }

        if (!$this->checkPayer($user_id)) {
            return 0;
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => Payment::class]);
        $builder->leftjoin(WorkflowRequestModel::class, 'request.biz_value = main.id', 'request');
        $builder->where('request.biz_type = :biz_type: AND request.state = :audit_state: AND request.is_abandon = :is_abandon:', [
            'biz_type' => $biz_type,
            'audit_state' => Enums::WF_STATE_APPROVED,
            'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO
        ]);
        $builder->andWhere('main.pay_status = :pay_status:', ['pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_BANKING]);
        $builder->columns('COUNT(DISTINCT main.id) AS total');
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 获取条件
     * @param $builder
     * @param $condition
     * @return mixed
     * @date 2022/3/11
     */
    public function getCondition($builder, $condition)
    {
        $no = $condition['no'] ?? [];   //申请单号
        $apply_staff_id = $condition['apply_staff_id'] ?? '';   //申请人或姓名
        $pay_status = $condition['pay_status'] ?? 0;    //支付状态
        $pay_method = $condition['pay_method'] ?? 0;    //支付方式
        $currency = $condition['currency'] ?? 0;        //货币额类型
        $apply_date_start = $condition['apply_date_start'] ?? '';   //申请日期开始时间
        $apply_date_end = $condition['apply_date_end'] ?? '';       //申请日期结束时间
        $flag = $condition['flag'] ?? 1;        //1待处理，2已处理，3在线支付
        $cost_company_id = $condition['cost_company_id'] ?? [];        //费用所属公司
        $pay_where = $condition['pay_where'] ?? '';        //境内境外支付
        $oa_type = $condition['oa_type'] ?? '';        //模块
        $ids = $condition['ids'] ?? [];        //勾选的列id
        $ticket_no = $condition['ticket_no'] ?? ''; //支票号
        $out_send_status = $condition['out_send_status'] ?? '';    //发送状态
        $out_send_at_start = $condition['out_send_at_start'] ?? '';   //发送日期开始时间
        $out_send_at_end = $condition['out_send_at_end'] ?? '';       //发送日期结束时间
        $pay_bank_account = $condition['pay_bank_account'] ?? [];//19606银行账号搜索多选
        $amount_total_actually_is_zero = $condition['amount_total_actually_is_zero'] ?? 0;//19606实付金额等于0单选是否
        if (isCountry(['TH','MY'])) {
            $planned_pay_date_start = $condition['planned_pay_date_start'] ?? '';//计划支付日期开始时间
            $planned_pay_date_end = $condition['planned_pay_date_end'] ?? '';//计划支付日期结束时间
            $overdue_unpaid = $condition['overdue_unpaid'] ?? 0;//到期未支付
            $bank_batch_number = $condition['bank_batch_number'] ?? '';//银行批次号查询
        }
        $out_trade_no = $condition['out_trade_no'] ?? '';//FlashPay交易号
        $builder->leftjoin(WorkflowRequestModel::class, 'request.biz_value = p.id', 'request');

        //16325需求，非在线支付列表
        if ($flag != GlobalEnums::PAYMENT_TAB_ONLINE) {
            // 取审批通过的数据, 待处理: 状态"银行支付中", 已处理: 状态"已支付","支付失败"
            $builder->where('request.biz_type = :biz_type: AND request.state = :audit_state: AND request.is_abandon = :is_abandon:', [
                'biz_type' => Enums::WF_PAY_TYPE,
                'audit_state' => Enums::WF_STATE_APPROVED,
                'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO
            ]);

            //在线支付的不需要查询支票信息
            $builder->leftjoin(PaymentCheck::class, 'pc.payment_id = p.id  and pc.is_deleted = ' . GlobalEnums::IS_NO_DELETED, 'pc');
        } else {
            // 取待审核、审核通过的数据，在线支付: 状态"pay支付中","pay支付失败","已支付"
            $builder->where('request.biz_type = :biz_type: AND request.state in({audit_state:array}) AND request.is_abandon = :is_abandon:', [
                'biz_type' => Enums::WF_PAY_TYPE,
                'audit_state' => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED],
                'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO
            ]);
        }
        switch ($flag) {
            //待处理
            case GlobalEnums::PAYMENT_TAB_PENDING:
                //16325需求，待处理只可看到银行支付中 && 非在线支付的单据
                $builder->andWhere('p.pay_status = :pay_status: and p.is_online_pay = :is_online_pay:', ['pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_BANKING, 'is_online_pay' => PayEnums::IS_ONLINE_PAY_NO]);
                break;

            //已处理
            case GlobalEnums::PAYMENT_TAB_PROCESSED:
                $builder->inWhere('p.pay_status', [PayEnums::PAYMENT_MODULE_PAY_STATUS_FAILED, PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY]);
                break;

            //在线支付
            case GlobalEnums::PAYMENT_TAB_ONLINE:
                //16325需求，只可看到在线支付的单据;pay支付中、pay支付失败、已支付
                $builder->inWhere('p.pay_status', [PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY, PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING, PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED]);
                $builder->andWhere('p.is_online_pay = :is_online_pay:', ['is_online_pay' => PayEnums::IS_ONLINE_PAY_YES]);
                break;
        }

        if (!empty($ids)) {
            $builder->inWhere('p.id', $ids);
        }

        if (!empty($no)) {
            if (is_array($no)) {
                $builder->inWhere('p.no', $no);
            } else {
                $builder->andWhere('p.no = :no:', ['no' => $no]);
            }
        }

        if (!empty($apply_staff_id)) {
            $builder->andWhere(
                'p.apply_staff_id = :apply_id: or p.apply_staff_name=:apply_id:',
                ['apply_id' => $apply_staff_id]
            );
        }

        if (!empty($pay_status)) {
            $builder->andWhere('p.pay_status = :pay_status:', ['pay_status' => $pay_status]);
        }

        if (!empty($pay_method)) {
            $builder->andWhere('p.pay_method = :pay_method:', ['pay_method' => $pay_method]);
        }

        if (!empty($currency)) {
            $builder->andWhere('p.currency = :currency:', ['currency' => $currency]);
        }

        if (!empty($apply_date_start)) {
            $builder->andWhere('p.apply_date >= :apply_date_start:', ['apply_date_start' => $apply_date_start]);
        }

        if (!empty($apply_date_end)) {
            $builder->andWhere('p.apply_date <= :apply_date_end:', ['apply_date_end' => $apply_date_end]);
        }

        if (!empty($cost_company_id)) {
            if (is_array($cost_company_id)) {
                $builder->andWhere('p.cost_company_id IN ({cost_company_id:array})', ['cost_company_id' => array_values($cost_company_id)]);
            } else {
                $builder->andWhere('p.cost_company_id = :cost_company_id:', ['cost_company_id' => $cost_company_id]);
            }
        }

        if (!empty($pay_where)) {
            $builder->andWhere('p.pay_where = :pay_where:', ['pay_where' => $pay_where]);
        }

        if (!empty($oa_type)) {
            $builder->andWhere('p.oa_type = :oa_type:', ['oa_type' => $oa_type]);
        }

        if (!empty($ticket_no)) {
            $builder->andWhere('pc.ticket_no = :ticket_no:', ['ticket_no' => $ticket_no]);
        }
        //发送状态
        if ($out_send_status !== '') {
            $builder->andWhere('p.out_send_status = :out_send_status:', ['out_send_status' => $out_send_status]);
        }
        //发送日期开始时间
        if (!empty($out_send_at_start)) {
            $builder->andWhere('p.out_send_at >= :out_send_at_start:', ['out_send_at_start' => $out_send_at_start . ' 00:00:00']);
        }
        //发送日期结束时间
        if (!empty($out_send_at_end)) {
            $builder->andWhere('p.out_send_at <= :out_send_at_end:', ['out_send_at_end' => $out_send_at_end  . ' 23:59:59']);
        }
        //银行账号
        if (!empty($pay_bank_account)) {
            $builder->inWhere('p.pay_bank_account', $pay_bank_account);
        }
        //实付金额等于0单选是否
        if (!empty($amount_total_actually_is_zero)) {
            $builder->andWhere('p.amount_total_actually ' . ($amount_total_actually_is_zero == PayEnums::IS_PAY_YES ? '=' : '>') . ' :amount_total_actually:', ['amount_total_actually' => 0]);
        }
        //计划支付日期开始时间
        if (!empty($planned_pay_date_start)) {
            $builder->andWhere('p.planned_pay_date >= :planned_pay_date_start:', ['planned_pay_date_start' => $planned_pay_date_start]);
        }
        //计划支付日期结束时间
        if (!empty($planned_pay_date_end)) {
            $builder->andWhere('p.planned_pay_date <= :planned_pay_date_end:', ['planned_pay_date_end' => $planned_pay_date_end]);
        }
        //到期未支付
        if (!empty($overdue_unpaid)) {
            $builder->andWhere('p.planned_pay_date <= :overdue_unpaid_date: and p.pay_status=:overdue_unpaid_pay_status:', ['overdue_unpaid_date' => date('Y-m-d'),'overdue_unpaid_pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING]);
        }
        //银行批次号
        if (!empty($bank_batch_number)) {
            $builder->andWhere('p.bank_batch_number=:bank_batch_number:', ['bank_batch_number' => $bank_batch_number]);
        }
        //FlashPay 交易号
        if (!empty($out_trade_no)) {
            $builder->andWhere('p.out_trade_no=:out_trade_no:', ['out_trade_no' => $out_trade_no]);
        }
        return $builder;
    }

    /**
     * 列表数据处理
     * @param $items
     * @return array
     * @date 2022/3/17
     */
    public function handleListItems($items)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        foreach ($items as &$item) {
            $item['oa_type_text'] = static::$t->_(Enums\BankFlowEnums::$oa_type_id_to_lang_key[$item['oa_type']]);
            $item['pay_status_text'] = static::$t->_(PayEnums::$payment_module_pay_status[$item['pay_status']]);
            //新增【实付金额总计】
            $currency_text = static::$t->_(GlobalEnums::$currency_item[$item['currency']]);
            $item['amount_total_actually_text'] = $item['amount_total_actually'] . ' ' . $currency_text;
            //新增 支付方式
            $item['pay_method_text'] = static::$t->_(Enums::$payment_method[$item['pay_method']]);
            //发送状态
            $item['out_send_status_text'] = static::$t->_(PayEnums::$out_send_status[$item['out_send_status']]);
            //发送时间
            $item['out_send_at'] = !is_null($item['out_send_at']) ? date('Y-m-d', strtotime($item['out_send_at'])) : '';
            //失败原因
            $item['out_trade_code_text'] = !is_null($item['out_trade_code']) ? static::$t->_(PayEnums::OUT_TRADE_CODE_PREFIX . $item['out_trade_code']) : '';
            //币种
            $item['currency_text'] = $currency_text;
            //是否有凭证
            $item['is_has_payment_voucher'] = boolval($item['is_has_payment_voucher']);
        }

        return $items;
    }

    /**
     * 详情
     * @param int $id 单据id
     * @param int $user_id 当前登录用户信息组
     * @param bool $check_payer 是否鉴权三级支付人
     * @return array
     */
    public function getDetail(int $id, int $user_id, $check_payer = true)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            if ($check_payer && !$this->checkPayer($user_id)) {
                throw new ValidationException(static::$t->_('pay_method_payer_error'), ErrCode::$VALIDATE_ERROR);
            }
            $item = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $id]
            ]);
            if (empty($item)) {
                throw new ValidationException('not found item', ErrCode::$VALIDATE_ERROR);
            }
            $service = new PayFlowService();
            $request = $service->getRequest($id);
            if (empty($request)) {
                throw new ValidationException('not found workflow', ErrCode::$VALIDATE_ERROR);
            }

            // 是否可评论
            $is_can_comment = false;
            if ($check_payer) {
                $is_can_comment = EnumsService::getInstance()->getSettingEnvValue('paymodule_bank_pay_step_is_comment') ? true : false;
            }

            $pays = $item->getPays();
            $pays = $pays ? $pays->toArray() : [];

            $attachments = $item->getFiles();
            $attachments = $attachments ? $attachments->toArray() : [];

            //支票支付方式-支付记录信息
            $checks = $item->getChecks();
            $pay_check = $checks ? $checks->toArray() : [];

            $data = $item->toArray();
            $data['pay_date'] = !empty($data['pay_date'])?date('Y-m-d',strtotime($data['pay_date'])):'';
            $data['pays'] = $pays;
            $data['attachments'] = $attachments;
            $data['supplements'] = $item->getSupplements()->toArray();
            $data['pay_check'] = $pay_check ? $pay_check :  (!empty($data['pay_check']) ? json_decode($data['pay_check'], 1) : []);
            $data['auth_logs'] = $service->getAuditLogs($data);
            $data['is_can_comment'] = $is_can_comment;
            //未支付原因分类
            $data['not_pay_reason_category_text'] = PayService::getInstance()->getNotPayReasonCategoryText($data);

                $this->handleDetailItem($data);
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('final-pay-getDetail-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 格式化详情
     * @param array $data 详情数据组
     */
    public function handleDetailItem(&$data)
    {
        $data['currency_text'] = static::$t->_(GlobalEnums::$currency_item[$data['currency']]);
        $data['pay_method_text'] = static::$t->_(Enums::$payment_method[$data['pay_method']]);
        $data['pay_where_text'] = static::$t->_(PayEnums::$pay_where_id_to_lang_key[$data['pay_where']]);
        $data['pay_bank_flow_date'] = $data['pay_bank_flow_date'] ?? '';
        $data['pay_date'] = $data['pay_date'] ?? '';
    }

    /**
     * 获取导出列表
     * @param int $staff_info_id 用户工号
     * @param array $condition 查询条件
     * @return array
     */
    public function getExportList($staff_info_id, $condition)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            //是否有支付权限
            if (!$this->checkPayer($staff_info_id)) {
                throw new ValidationException(static::$t->_('pay_method_payer_error'), ErrCode::$VALIDATE_ERROR);
            }
            $builder = $this->modelsManager->createBuilder();
            $builder->columns(
                'distinct p.id,p.oa_type,p.no,p.apply_staff_id,p.apply_staff_name,p.cost_company_name,p.cost_department_name,p.pay_method,p.is_pay,p.pay_bank_name,p.pay_bank_account,p.pay_date,p.pay_bank_flow_date,p.not_pay_reason,p.pay_remark,p.pay_check,p.not_pay_reason,
                pp.bank_account_name,pp.bank_name,pp.bank_account,p.currency,p.amount_remark,pp.amount,
                p.amount_total_actually,p.amount_total_no_tax,p.amount_total_vat,p.amount_total_have_tax,p.amount_total_wht,p.amount_total_have_tax_no_wht,
                p.amount_loan,p.amount_reserve,p.amount_discount,p.default_planned_pay_date,p.planned_pay_date,p.bank_batch_number,p.bank_pay_type'
            );
            $builder->from(['p' => Payment::class]);
            $builder->leftjoin(PaymentPay::class, 'pp.payment_id = p.id', 'pp');
            $builder = $this->getCondition($builder, $condition);
            $items = $builder->getQuery()->execute()->toArray();
            $this->handleExportListItems($items);

            $header = [
                static::$t->_('payment_export_module'),             //模块
                static::$t->_('global.number'),             //编号
                static::$t->_('global.applicant.name'),     //申请人姓名
                static::$t->_('global.applicant.id'),       //申请人工号
                static::$t->_('re_field_apply_company_name'),   //申请人所属公司
                static::$t->_('bank_flow_export_field_create_department_name'),    //申请人所属部门
                static::$t->_('pay_field_pay_method'),      //支付方式
                static::$t->_('pay_field_currency'),            //支付币种
                static::$t->_('global.remark'),                 //备注
                static::$t->_('pay_field_bank_account_name'),   //收款人银行账户名称
                static::$t->_('pay_field_bank_name'),           //收款银行名称
                static::$t->_('pay_field_bank_account'),        //收款账号
                static::$t->_('pay_field_account'),             //支付金额
                static::$t->_('total.amount.actually.paid'),     //实付金额总计
                static::$t->_('payment_store_renting_no_total_amount'), //不含税金额总计
                static::$t->_('payment_store_renting_vat_total_amount'), //vat总计
                static::$t->_('pay_field_amount_total_have_tax'),       // 含税金额总计（含VAT含WHT）
                static::$t->_('payment_store_renting_wht_total_amount'),    //wht金额总计
                static::$t->_('pay_field_amount_total_have_tax_no_wht'),    //含税金额总计（含VAT不含WHT）
                static::$t->_('pay_field_amount_loan'),                 //冲减借款金额
                static::$t->_('pay_field_amount_reserve'),              //冲减备用金金额
                static::$t->_('purchase.discount'),                      //折扣
                static::$t->_('payment_export_is_pay'),                      //是否支付
            ];
            if (isCountry(['TH','MY'])) {
                $header[] = static::$t->_('planned_pay_date');        //计划支付日期
                $header[] = static::$t->_('default_planned_pay_date');//应付日期(默认计划支付日期)
            }
            $header[] = static::$t->_('payment_export_pay_bank_name');                         //支付银行
            $header[] = static::$t->_('payment_export_pay_bank_account');                      //银行账号
            if (get_country_code() == 'MY') {
                $header[] = static::$t->_('bank_batch_number');//银行批次号
                $header[] = static::$t->_('bank_pay_type');    //银行支付方式
            }
            $header[] = static::$t->_('payment_export_pay_date');                               //支付日期
            $header[] = static::$t->_('payment_export_pay_check.ticket_no');                    //支票号
            $header[] = static::$t->_('payment_export_pay_check.date');                         //承兑日期
            $header[] = static::$t->_('payment_export_pay_check.amount');                       //支票金额
            $header[] = static::$t->_('payment_export_pay_check.payee_name');                   //收款人名称
            $header[] = static::$t->_('payment_export_pay_bank_flow_date');                     //银行流水日期
            $header[] = static::$t->_('payment_export_pay_remark');                             //备注
            $header[] = static::$t->_('payment_export_not_pay_reason');                         //未支付原因



            $exportData = [];

            $fields = ['oa_type_text','no','apply_staff_name','apply_staff_id','cost_company_name','cost_department_name','pay_method_text','currency_text','amount_remark','bank_account_name','bank_name','bank_account',
                'amount','amount_total_actually','amount_total_no_tax','amount_total_vat','amount_total_have_tax','amount_total_wht',
                'amount_total_have_tax_no_wht','amount_loan','amount_reserve','amount_discount','is_pay_text',];
            if (isCountry(['TH','MY'])) {
                $fields[] = 'planned_pay_date';        //计划支付日期
                $fields[] = 'default_planned_pay_date';//应付日期(默认计划支付日期)
            }

            $fields[] = 'pay_bank_name';
            $fields[] = 'pay_bank_account';
            if (get_country_code() == 'MY') {
                $fields[] = 'bank_batch_number';     //银行批次号
                $fields[] = 'bank_pay_type_text';//银行支付方式
            }
            $fields[] = 'pay_date';
            $fields[] = 'pay_check_ticket_no';
            $fields[] = 'pay_check_date';
            $fields[] = 'pay_check_amount';
            $fields[] = 'pay_check_payee_name';
            $fields[] = 'pay_bank_flow_date';
            $fields[] = 'pay_remark';
            $fields[] = 'not_pay_reason';
            foreach ($items as $item) {
                $tmp = [];
                foreach ($fields as $field) {
                    $tmp[] = $item[$field];
                }
                $exportData[] = $tmp;
            }

            $res = $this->exportExcel($header, $exportData);
            $data = $res['data'];
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error('pay-finalPay-getList-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 处理导出列表数据
     * @param array $items 导出列表
     * @return array
     */
    public function handleExportListItems(&$items)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }

        ini_set('memory_limit', '1024M');

        /**
         * 13784【MY|OA|银行流水】 银行流水及支付模块迁移
         * https://flashexpress.feishu.cn/docx/doxcn8oZcw7n1PUJRdbcOuHdeTd
         * payment——pay_check字段为支付模块的支付信息，现在表中储存了jason字段，本次将其改成附表形式。
         * 新数据走表，老数据还是读pay_check字段
         */
        $payment_id_arr = array_column($items, 'id');
        $payment_check_list = PaymentCheck::find([
            'conditions' => 'payment_id in ({ids:array}) and is_deleted = :is_deleted:',
            'bind' => ['ids' => $payment_id_arr, 'is_deleted' => 0]
        ])->toArray();
        $pay_check_data = [];
        if (!empty($payment_check_list)) {
            foreach ($payment_check_list as $item) {
                $pay_check_data[$item['payment_id']] [] = $item;
            }
        }
        $bankPaymentTypeMap = PayService::getInstance()->getBankPaymentTypeMap();
        foreach ($items as &$item) {
            $item['oa_type_text'] = static::$t->_(Enums\BankFlowEnums::$oa_type_id_to_lang_key[$item['oa_type']]);
            $item['pay_method_text'] = static::$t->_(Enums::$payment_method[$item['pay_method']]);
            $item['currency_text'] = static::$t->_(GlobalEnums::$currency_item[$item['currency']]);
            $item['is_pay_text'] = static::$t->_(PayEnums::$payment_is_pay_key[$item['is_pay']]);
            $item['bank_pay_type'] = empty($item['bank_pay_type']) ? '' : $item['bank_pay_type'];
            $item['bank_pay_type_text'] = empty($item['bank_pay_type']) ? '' : ($bankPaymentTypeMap[$item['bank_pay_type']] ?? '');
            //支票
            $item['pay_check_ticket_no'] = '';
            $item['pay_check_date'] = '';
            $item['pay_check_amount'] = '';
            $item['pay_check_payee_name'] = '';
            if ($item['pay_method'] == Enums::PAYMENT_METHOD_CHECK) {
                //支票支付方式，才做如下逻辑处理
                if (!empty($pay_check_data[$item['id']])) {
                    //表里存在发票信息
                    $pay_check_array = $pay_check_data[$item['id']];
                } else {
                    $pay_check_array = json_decode($item['pay_check'],true);
                }
                if (!empty($pay_check_array)) {
                    $pay_check_ticket_no = array_column($pay_check_array,'ticket_no');
                    $pay_check_date = array_column($pay_check_array,'date');
                    $pay_check_amount = array_column($pay_check_array,'amount');
                    $pay_check_payee_name = array_column($pay_check_array,'payee_name');
                    $item['pay_check_ticket_no'] = implode(';',$pay_check_ticket_no);
                    $item['pay_check_date'] = implode(';',$pay_check_date);
                    $item['pay_check_amount'] = implode(';',$pay_check_amount);
                    $item['pay_check_payee_name'] = implode(';',$pay_check_payee_name);
                }
            }
        }
    }

    /**
     * @param $itemArr //payment表数据的array
     * @param $user //当前登录用户, controllers里的$this->user
     * @param int $before_pay_status  本次操作前的支付状态
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     * @date 2022/3/7
     */
    public function businessPay($itemArr, $user, $before_pay_status = 0)
    {
        $model = BankFlowPayFlowService::getInstance()->getModelByTypeId($itemArr['oa_type']);
        $model = $model->getModelByNo($itemArr['no']);
        if (empty($model) || $model->pay_status != 1) {
            throw new ValidationException('not found model or model pay_status is error.' . $itemArr['no']);
        }
        $payData = $model->getPayCallBackData($itemArr);
        $res = [];
        switch ($itemArr['oa_type']) {
            case Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_LOAN:
                $payData['id'] = $model->id;
                $res = LoanUpdateService::getInstance()->pay($model->id, $payData, $user, PayEnums::IS_FROM_PAY);
                break;
            case Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT:
                $res = UpdateService::getInstance()->pay($model->id, $payData, $user, PayEnums::IS_FROM_PAY);
                break;
            case Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT:
                //待支付状态下撤回,业务单中的未支付原因 = 撤回原因
                if ($before_pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING && $itemArr['pay_status'] == PayEnums::PAYMENT_MODULE_PAY_STATUS_NOTPAY) {
                    $payData['note'] = $itemArr['cancel_reason'];
                }
                $res = PaymentService::getInstance()->pay($model->id, $payData, $user, PayEnums::IS_FROM_PAY);
                break;
            case Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING:
                $payData['id'] = $model->id;
                $res = StoreRentingUpdateService::getInstance()->pay($model->id, $payData, $user, PayEnums::IS_FROM_PAY);
                break;
            case Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT:
                $payData['id'] = $model->id;
                $res = OrdinaryPaymentUpdateService::getInstance()->pay($payData, $user, PayEnums::IS_FROM_PAY);
                break;
            case Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_RESERVE_FUND:
                //待支付状态下撤回,业务单中的未支付原因 = 撤回原因
                if ($before_pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING && $itemArr['pay_status'] == PayEnums::PAYMENT_MODULE_PAY_STATUS_NOTPAY) {
                    $payData['note'] = $itemArr['cancel_reason'];
                }
                $res = ApplyService::getInstance()->pay($model->id, $payData, $user, PayEnums::IS_FROM_PAY);
                break;
            case Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_WAGE:
                //薪酬扣款
                $res = (new WagesUpdateService())->updatePayment($model->id, $payData, $user, PayEnums::IS_FROM_PAY, $itemArr);
                break;
            case Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_SALARY:
                //薪资发放
                $res = (new SalaryUpdateService())->updatePayment($model->id, $payData, $user, PayEnums::IS_FROM_PAY);
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT:
                //代理支付
                $res = AgencyPaymentPayService::getInstance()->pay($model->id, $payData, $user, PayEnums::IS_FROM_PAY, $itemArr);
                break;
            default:
                throw new ValidationException('not found oa type=' . $itemArr['oa_type']);
                break;
        }

        if ($res['code'] != 1) {
            throw new ValidationException($res['message']);
        }
        return true;
    }

    /**
     * 支付选择 是或否
     * @param $params
     * @param $user
     * @param $is_batch
     * @return array
     * @date 2022/5/16
     */
    public function submit($params, $user, $is_batch = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {

            //判断是否是支付人
            if (!$this->checkPayer($user['id'])) {
                throw new ValidationException(static::$t->_('pay_method_payer_error'), ErrCode::$VALIDATE_ERROR);
            }
            //先校验单据是否存在
            $item = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']]
            ]);
            if (empty($item)) {
                throw new ValidationException('not found the item', ErrCode::$VALIDATE_ERROR);
            }
            //16325需求新增仅可对支付状态为“银行支付中”的单据，进行支付。
            if ($item->pay_status != PayEnums::PAYMENT_MODULE_PAY_STATUS_BANKING) {
                throw new ValidationException(static::$t->_('payment_pay_status_not_banking'), ErrCode::$VALIDATE_ERROR);
            }
            $country = get_country_code();
            //支付方式=支票，需要检测支票信息合法性;16325需求需要区分是否对接了支票模块，对接了支票模块走该逻辑
            if ($is_batch && $params['pay_method'] == GlobalEnums::PAYMENT_METHOD_CHECK && PayService::getInstance()->checkChequeIsOpen()) {
                PayService::getInstance()->getEditPaymentCheck($params);

                $ticket_nos = array_unique(array_column($params['pay_check'], 'ticket_no'));
                if (!empty($ticket_nos) && $country == GlobalEnums::PH_COUNTRY_CODE && $params['is_pay'] == PayEnums::IS_PAY_YES) {
                    $ticket_nos  = "'" . implode("','", $ticket_nos) . "'";
                    $update_bool = $db->updateAsDict(
                        (new ChequeAccountModel())->getSource(),
                        [
                            'release_status' => Enums\ChequeEnums::CHEQUE_ACCOUNT_RELEASE_DONE,
                            'release_at'     => date('Y-m-d H:i:s'),
                            'updated_at'     => date('Y-m-d H:i:s')

                        ],
                        ['conditions' => "cheque_code in ($ticket_nos)"]
                    );
                    if ($update_bool === false) {
                        throw new BusinessException('银行支付-更新支票释放状态数据是：' . json_encode(['cheque_code' => $ticket_nos], JSON_UNESCAPED_UNICODE), ErrCode::$CHEQUE_ACCOUNT_RELEASE_STATUS_SAVE_ERROR);
                    }
                }
            }
            $tmp = [];
            $pay = new PayFlowService();
            $item->updated_at = gmdate('Y-m-d H:i:s');    //更新时间，保持created_at，updated_at，payer_date 0时区存储格式
            //支付=是的时候调用通过
            if ($params['is_pay'] == PayEnums::IS_PAY_YES) {
                //附件
                $tmp['attachments'] = $params['attachments'] ?? [];
                //支付备注
                $tmp['pay_remark'] = $params['pay_remark'] ?? '';

                //现金,支票只能修改备注和附件,银行转账可以修改流水日期
                if ($item->pay_method == Enums::PAYMENT_METHOD_BANK_TRANSFER && isset($params['pay_bank_flow_date'])) {
                    if ($is_batch) {
                        //16325需求，若三级支付人修改的银行流水日期要存储,空也要清空存储
                        $tmp['pay_bank_flow_date'] = $params['pay_bank_flow_date'];
                    } else if (!empty($params['pay_bank_flow_date'])) {
                        //19606需求，若三级支付人修改的银行流水日期要存储,批量操作银行流水日期空不要清空存储
                        $tmp['pay_bank_flow_date'] = $params['pay_bank_flow_date'];
                    }
                }
                $tmp['is_pay'] = $params['is_pay'];
                //修改支付单信息
                $can_edit_field = ['main'=>['pay_bank_flow_date','pay_remark'],'meta'=>['attachments']];
                $update_payment_result = $pay->dealEditField($item, $can_edit_field, $tmp, $user);
                if (!$update_payment_result) {
                    throw new BusinessException('update payment failed', ErrCode::$PAYMENT_UPDATE_PAY_DATA_ERROR);
                }
                //更改字段
                $item->is_pay = PayEnums::IS_PAY_YES;
                $item->pay_status = PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY;
                $item->payer_id = $user['id'];
                $item->payer_date = gmdate('Y-m-d H:i:s');
                if ($item->save() === false) {
                    throw new BusinessException('update payment failed', ErrCode::$PAYMENT_FINAL_PAYMENT_SAVE_ERROR);
                }
                $itemArr = $item->toArray();
                //业务数据支付
                $item_arr = array_merge($itemArr, ['send_crowd' => true]);
                $pay_result = $this->businessPay($item_arr, $user);
                if (!$pay_result) {
                    throw new BusinessException('final pay failed', ErrCode::$PAYMENT_FINAL_PAY_ERROR);
                }

                //支付为是的时候 如果是众包数据 需要通知众包系统
                if ($item->oa_type == BankFlowEnums::BANK_FLOW_OA_TYPE_WAGE && $country == GlobalEnums::TH_COUNTRY_CODE) {
                    $wages = WagesModel::findFirst([
                        'conditions' => 'no = :no:',
                        'bind'       => ['no' => $item->no]
                    ]);
                    if (!empty($wages)) {
                        if ($wages->apply_type == WagesEnums::APPLY_TYPE_CROWD_SOURCING) {
                            (new AddService())->postCrowdSourcingPayStatus($wages, $item);
                        } else if ($wages->apply_type == WagesEnums::APPLY_TYPE_PERSONAL_AGENT) {
                            (new AddService())->postBIPayStatus($wages, $item);
                        }
                    }
                }
            } else {
                $note = $params['not_pay_reason'];
                $item->is_pay = PayEnums::IS_PAY_NO;
                $item->not_pay_reason = $note;
                if (PayService::getInstance()->needNotPayReasonsCategory($item->toArray())) {
                    if (empty($params['not_pay_reason_category'])) {
                        throw new ValidationException(static::$t->_('not_pay_reason_category_is_required'),
                            ErrCode::$VALIDATE_ERROR);
                    }
                    $item->not_pay_reason_category = $params['not_pay_reason_category'];
                }
                $item->pay_status = PayEnums::PAYMENT_MODULE_PAY_STATUS_FAILED;
                $item->payer_id = $user['id'];
                $item->payer_date = gmdate('Y-m-d H:i:s');
                if ($item->save() === false) {
                    throw new BusinessException('update payment failed', ErrCode::$PAYMENT_FINAL_PAYMENT_SAVE_ERROR);
                }

                //16325需求需要区分是否对接了支票模块，对接了支票模块走该逻辑
                if (PayService::getInstance()->checkChequeIsOpen()) {
                    //支付为否的时候调用
                    $revoke_cheque_account = ChequeService::getInstance()->revokeChequeAccount($params['id'], $db);
                    if ($revoke_cheque_account['code'] != ErrCode::$SUCCESS) {
                        throw new ValidationException(static::$t->_('cheque_account_update_fail'), ErrCode::$CHEQUE_ACCOUNT_UPDATE_ERROR);
                    }
                    if ($item->pay_method == Enums::PAYMENT_METHOD_CHECK) {
                        $old_checks_list = $item->getChecks();
                        $old_checks_arr = $old_checks_list->toArray();
                        if (!empty($old_checks_arr)) {
                            $ids                  = implode(',', array_column($old_checks_arr, 'id'));
                            $update_payment_check = $db->updateAsDict(
                                (new PaymentCheck())->getSource(),
                                [
                                    'is_deleted' => GlobalEnums::IS_DELETED,
                                    'updated_at' => date('Y-m-d H:i:s', time())
                                ],
                                [
                                    'conditions' => " id IN ($ids)",
                                ]
                            );
                            if ($update_payment_check === false) {
                                throw new BusinessException('支票数据情况老数据失败, 数据: ' . json_encode($old_checks_arr, JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($update_payment_check), ErrCode::$BUSINESS_ERROR);
                            }
                        }
                    }
                }
                //不支付  发送邮件
                PayService::getInstance()->sendEmail($item, [$item->apply_staff_id]);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $db->rollback();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('final_pay-submit-failed:code='. $code.' ;message='. $e->getMessage().'; trace='.$e->getTraceAsString());
            $db->rollback();
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 1. 如果选择的合并支付的数据包含了代理支付和其他模块的数据，则在点击合并支付时提示“代理支付不允许与其他模块合并支付！”
     * @param $data
     * @return true
     * @throws ValidationException
     */
    protected function validateUniquePayModule($data)
    {
        if (get_country_code() != GlobalEnums::PH_COUNTRY_CODE) {
            return true;
        }

        if (empty($data['bank_batch'])) {
            return true;
        }
        $payment_ids = [];

        foreach ($data['bank_batch'] as $item) {
            $payment_ids = array_merge($payment_ids, $item['payment_ids']);
        }
        $module_payment_ids = Payment::find([
            'conditions' => 'id IN ({payment_ids:array})',
            'bind'       => ['payment_ids' => $payment_ids],
            'columns'    => 'oa_type',
        ])->toArray();
        $unique_oa_type     = array_unique(array_column($module_payment_ids, 'oa_type'));
        if (count($unique_oa_type) > 1 && in_array(BankFlowEnums::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT, $unique_oa_type)) {
            throw new ValidationException(static::$t->_('proxy_payment_is_not_allowed_to_be_combined_with_other_modules'),
                ErrCode::$VALIDATE_ERROR);
        }
        return true;

    }
    /**
     * 合并支付
     * @Date: 1/13/23 7:06 PM
     * @param array $data
     * @param array $user
     * @return bool
     * @author: peak pan
     * @throws \Exception
     **/
    public function submitBatch(array $data, array $user)
    {
        //开启事务
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //19606增加了银行支付方式的合并支付,所以需要区分支付方式
            if ($data['pay_method'] == GlobalEnums::PAYMENT_METHOD_BANK_TRANSFER) {
                //22258 1. 如果选择的合并支付的数据包含了代理支付和其他模块的数据，则在点击合并支付时提示“代理支付不允许与其他模块合并支付！”
                $this->validateUniquePayModule($data);

                foreach ($data['bank_batch'] as $item) {
                    foreach ($item['payment_ids'] as $id) {
                        $audit_data = [
                            'is_pay' => $data['is_pay'],
                            'pay_method' => $data['pay_method'],
                            'not_pay_reason' => $data['not_pay_reason'] ?? '',
                            'not_pay_reason_category' => $data['not_pay_reason_category'] ?? null,
                            'pay_bank_flow_date' => $item['pay_bank_flow_date'] ?? '',
                            'pay_remark' => $data['pay_remark'] ?? '',
                            'attachments' => $data['attachments'] ?? [],
                            'id' => $id
                        ];
                        $audit_result = $this->submit($audit_data, $user);
                        if (!isset($audit_result['code']) || $audit_result['code'] != ErrCode::$SUCCESS) {
                            throw new BusinessException('submit batch failed, payment_id=' . $id .' msg:'.$audit_result['message']??'', ErrCode::$PAYMENT_FINAL_PAYMENT_SAVE_ERROR);
                        }
                    }
                }
            } else {
                $audit_data = $data;
                unset($audit_data['id_batch']);
                foreach ($data['id_batch'] as $id) {
                    $audit_data['id'] = $id;
                    $audit_result     = $this->submit($audit_data, $user);
                    if (!isset($audit_result['code']) || $audit_result['code'] != ErrCode::$SUCCESS) {
                        throw new BusinessException('submit batch failed, payment_id=' . $id, ErrCode::$PAYMENT_FINAL_PAYMENT_SAVE_ERROR);
                    }
                }
            }
            $db->commit();

        } catch (\Exception $e) {
            $this->logger->error('final_pay-submit-batch:code='. $e->getCode().' ;message='. $e->getMessage().'; trace='.$e->getTraceAsString());
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 在线支付-异步导出-任务添加
     * @param int $staff_info_id 用户工号
     * @param array $params 查询条件
     * @param bool $check_payer
     * @return mixed
     * @throws ValidationException
     */
    public function onlineExportTask($staff_info_id, $params, $check_payer = true)
    {
        //是否有支付权限
        if ($check_payer && !$this->checkPayer($staff_info_id)) {
            throw new ValidationException(static::$t->_('pay_method_payer_error'), ErrCode::$VALIDATE_ERROR);
        }

        return DownloadCenterService::getInstance()->addDownloadCenter($staff_info_id, DownloadCenterEnum::PAYMENT_ONLINE, $params);
    }

    /**
     * 获取在线支付导出的表头
     * @return array
     */
    public function getExportExcelHeaderFields()
    {
        $header = [];
        $header[] = static::$t->_('global.number');                            //编号
        $header[] = static::$t->_('payment_pay_online_out_trade_no');          //FlashPay 交易号
        $header[] = static::$t->_('global_pay_status');                        //支付状态
        $header[] = static::$t->_('global.applicant.name');                    //申请人姓名
        if (isCountry('TH')) {
            $header[] = static::$t->_('planned_pay_date');                    //计划支付日期
        }
        $header[] = static::$t->_('global.applicant.id');                      //申请人工号
        $header[] = static::$t->_('re_field_apply_company_name');              //费用所属公司
        $header[] = static::$t->_('payment_pay_online_create_department_name');//费用所属部门
        $header[] = static::$t->_('pay_field_pay_method');                     //支付方式
        $header[] = static::$t->_('pay_field_currency');                       //支付币种
        $header[] = static::$t->_('global.remark');                            //备注
        $header[] = static::$t->_('pay_field_bank_account_name');              //收款人银行账户名称
        $header[] = static::$t->_('pay_field_bank_name');                      //收款银行名称
        $header[] = static::$t->_('pay_field_bank_account');                   //收款账号
        $header[] = static::$t->_('total.amount.actually.paid');               //实付金额总计
        $header[] = static::$t->_('payment_export_pay_bank_name');             //支付银行
        $header[] = static::$t->_('payment_export_pay_bank_account');          //银行账号
        $header[] = static::$t->_('payment_export_pay_bank_flow_date');        //银行流水日期
        $header[] = static::$t->_('payment_pay_online_out_send_status');       //发送状态
        $header[] = static::$t->_('payment_pay_online_out_trade_at');          //发送日期
        $header[] = static::$t->_('payment_pay_online_out_trade_code');        //错误码
        $header[] = static::$t->_('payment_pay_online_out_trade_code_text');   //错误原因
        $header[] = static::$t->_('payment_pay_online_first_payer_id');        //一级支付人工号
        $header[] = static::$t->_('payment_pay_online_first_payer_name');      //一级支付人姓名
        $header[] = static::$t->_('payment_pay_online_second_payer_id');       //二级支付人工号
        $header[] = static::$t->_('payment_pay_online_second_payer_name');     //二级支付人姓名
        if (isCountry(['TH', 'MY', 'PH'])) {
            $header[] = static::$t->_('22402_flash_pay_payment_voucher');          //FlashPay支付凭证
        }

        return $header;
    }

    /**
     * 获取导出条数
     * @param array $condition 查询条件组
     * @return int
     */
    public function getExportOnlineListCount($condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(DISTINCT p.id) AS count');
        $builder->from(['p' => Payment::class]);
        $builder->leftjoin(PaymentPay::class, 'pp.payment_id = p.id', 'pp');
        //组合搜索条件
        $builder = $this->getCondition($builder, $condition);
        return intval($builder->getQuery()->getSingleResult()->count);
    }

    /**
     * 获取在线支付导出列表
     * @param array $condition 查询条件
     * @return array
     */
    public function getExportOnlineList($condition)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
            $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
            $offset = $page_size * ($page_num - 1);

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('p.no,p.out_trade_no,p.pay_status,p.apply_staff_id,p.apply_staff_name,p.cost_company_name,p.cost_department_name,
                p.pay_method,p.currency,p.pay_remark,pp.bank_account_name,pp.bank_name,pp.bank_account,p.amount_total_actually,
                p.pay_bank_name,p.pay_bank_account,p.pay_bank_flow_date,p.out_send_status,p.out_send_at,out_trade_code,request.id as request_id, request.flow_id,request.viewer_ids,p.default_planned_pay_date,p.planned_pay_date,p.payment_voucher');
            $builder->from(['p' => Payment::class]);
            $builder->leftjoin(PaymentPay::class, 'pp.payment_id = p.id', 'pp');
            $builder = $this->getCondition($builder, $condition);
            $builder->groupBy('p.id');
            $builder->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();
            $data = $this->handleExportOnlineListItems($items);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('payment_export_online_list-failed:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 格式化在线支付导出信息组
     * @param array $items 导出数据组
     * @return array
     */
    public function handleExportOnlineListItems($items)
    {
        $exportData = [];
        if (empty($items) || !is_array($items)) {
            return [];
        }
        //审批请求ID组
        $request_ids = array_unique(array_filter(array_column($items, 'request_id')));
        //审批流ID组
        $request_flow_id = array_unique(array_filter(array_column($items, 'flow_id')));
        //审批人工号组
        $request_viewer_ids = array_unique(array_filter(explode(',', implode(',', array_column($items, 'viewer_ids')))));

        //查询每笔单据的一级支付人、二级支付人;最新的一条记录
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('MAX(audit_log.id) as id');
        $builder->from(['audit_log' => WorkflowAuditLogModel::class]);
        $builder->leftjoin(WorkflowNodeModel::class, 'node.id = audit_log.flow_node_id', 'node');
        $builder->where('node.type = :type: and audit_log.audit_action > :audit_action:', ['type' => Enums::WF_NODE_ACTION, 'audit_action' => Enums::WF_ACTION_APPLY]);//审批动作
        $builder->inWhere('audit_log.request_id', $request_ids);
        $builder->inWhere('audit_log.staff_id', $request_viewer_ids);
        $builder->inWhere('node.flow_id', $request_flow_id);
        $builder->groupBy('audit_log.request_id, node.id');
        $log_list = $builder->getQuery()->execute()->toArray();
        $approve_user_log_list = [];
        if (!empty($log_list)) {
            $log_ids = array_values(array_column($log_list, 'id'));
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('request_id,staff_id,staff_name');
            $builder->from(WorkflowAuditLogModel::class);
            $builder->inWhere('id', $log_ids);
            $approve_user_list = $builder->getQuery()->execute()->toArray();
            if (!empty($approve_user_list)) {
                foreach ($approve_user_list as $user) {
                    $approve_user_log_list[$user['request_id']][] = [
                        'staff_id' => $user['staff_id'],
                        'staff_name' => $user['staff_name']
                    ];
                }
            }
        }

        foreach ($items as $item) {
            $payer_info = !empty($approve_user_log_list[$item['request_id']]) ? $approve_user_log_list[$item['request_id']] : [];
            $first_payer_info = !empty($payer_info[0]) ? $payer_info[0] : [];//一级支付人
            $second_payer_info = !empty($payer_info[1]) ? $payer_info[1] : [];//二级支付人
            $tmp = [];
            $tmp[] =  $item['no'];//编号
            $tmp[] =  $item['out_trade_no'];//FlashPay 交易号
            $tmp[] =  $item['pay_status'] ? static::$t->_(PayEnums::$payment_module_pay_status[$item['pay_status']]) : '';//支付状态
            $tmp[] =  $item['apply_staff_name'];//申请人姓名
            $tmp = $this->exportBuildOtherData($tmp,$item);//差异化参数
            $tmp[] =  $item['apply_staff_id'];//申请人工号
            $tmp[] =  $item['cost_company_name'];//费用所属公司
            $tmp[] =  $item['cost_department_name'];//费用所属部门
            $tmp[] =  $item['pay_method'] ? static::$t->_(Enums::$payment_method[$item['pay_method']]) : '';//支付方式
            $tmp[] =  $item['currency'] ? static::$t->_(GlobalEnums::$currency_item[$item['currency']]) : '';//支付币种
            $tmp[] =  $item['pay_remark'];//备注
            $tmp[] =  $item['bank_account_name'];//收款人银行账户名称
            $tmp[] =  $item['bank_name'];//收款银行名称
            $tmp[] =  $item['bank_account'];//收款账号
            $tmp[] =  $item['amount_total_actually'];//实付金额总计
            $tmp[] =  $item['pay_bank_name'];//支付银行
            $tmp[] =  $item['pay_bank_account'];//银行账号
            $tmp[] =  $item['pay_bank_flow_date'] ? date('Y-m-d', strtotime($item['pay_bank_flow_date'])) : '';//银行流水日期
            $tmp[] =  static::$t->_(PayEnums::$out_send_status[$item['out_send_status']]);//银行流水日期
            $tmp[] =  $item['out_send_at'] ? date('Y-m-d', strtotime($item['out_send_at'])) : '';//发送日期
            $tmp[] =  $item['out_trade_code'] != 0 ? $item['out_trade_code'] : '';//错误码
            $tmp[] =  (!is_null($item['out_trade_code']) && $item['out_trade_code'] != 0) ? static::$t->_(PayEnums::OUT_TRADE_CODE_PREFIX . $item['out_trade_code']) : '';//错误原因
            $tmp[] =  $first_payer_info['staff_id'] ?? '';//一级支付人工号
            $tmp[] =  $first_payer_info['staff_name'] ?? '';//一级支付人姓名
            $tmp[] =  $second_payer_info['staff_id'] ?? '';//二级支付人工号
            $tmp[] =  $second_payer_info['staff_name'] ?? '';//二级支付人姓名
            if (isCountry(['TH', 'MY', 'PH'])) {
                $tmp[] = $item['payment_voucher'];//FlashPay支付凭证
            }
            $exportData[] =  $tmp;
        }
        return $exportData;
    }

    protected function exportBuildOtherData($tmp,$items)
    {
        if(isCountry('TH')){
            $tmp[]= $items['planned_pay_date'];//计划支付日期
        }
        return $tmp;

    }

    /**
     * 撤回
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @param bool $check_payer 是否鉴权三级支付人
     * @return array
     */
    public function cancel($params, $user, $check_payer = true)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $bool = false;
        try {
            //是否有支付权限
            if ($check_payer && !$this->checkPayer($user['id'])) {
                throw new ValidationException(static::$t->_('pay_method_payer_error'), ErrCode::$VALIDATE_ERROR);
            }
            //查询数据
            $item = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']],
            ]);
            if (empty($item)) {
                throw new ValidationException(static::$t->_('payment_pay_info_not_found'), ErrCode::$VALIDATE_ERROR);
            }
            //支付状态 = pay支付中 && 发送状态=未传输的才可撤回
            if ($item->pay_status != PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING || $item->out_send_status != PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_NO) {
                throw new ValidationException(static::$t->_('payment_pay_can_not_cancel'), ErrCode::$VALIDATE_ERROR);
            }

            //撤回单据流转到第3级支付人的待处理界面，支付状态pay_status = 银行支付中（6）；此时该单据不可再触发给pay系统，在线支付is_online_pay=否（0）
            $update_data = [
                'pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_BANKING,//银行支付中
                'is_online_pay' => PayEnums::IS_ONLINE_PAY_NO,//非在线支付
                'payer_cancel_reason' => $params['reason'],
                'payer_cancel_id' => $user['id'],
                'payer_cancel_at' => gmdate('Y-m-d H:i:s'),//与本模块申请人撤回操作日期格式存储一致
                'updated_at' => gmdate('Y-m-d H:i:s')//更新时间，保持created_at，updated_at，payer_date 0时区存储格式
            ];
            $bool = $item->i_update($update_data);
            if ($bool === false) {
                throw new BusinessException('在线支付单据-三级审批人-撤回[失败]: 待处理数据: '. json_encode($update_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($item), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        }  catch (BusinessException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if ($real_message) {
            $this->logger->error('payment_pay_online_cancel failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $bool
        ];
    }

    /**
     * pay支付失败的单据，需要驳回到一级审批人，重新走支付审批
     * @param object $payment 支付单据对象信息组
     * @param array $user_id 三级支付人用户id
     * @throws BusinessException
     */
    public function flashPayFailedReject($payment, $user_id)
    {
        //获取审批流请求
        $pay = new PayFlowService();
        $request = $pay->getRequest($payment->id);

        //记录驳回原因
        $reject_reason = static::$t->_('payment_pay_online_failed_reject_reason');
        $us = new UserService();
        $user = $us->getUserById($user_id);
        $user = (new BaseController())->format_user($user);
        WorkflowServiceV2::getInstance()->saveAuditLog($request, 0, $user, Enums::WF_ACTION_REJECT, $reject_reason);

        //pay支付失败，系统自动替申请人发起重新审批流程
        return $pay->recommit($payment, []);
    }

    /**
     * 获取自动支付人
     * 需求17763规则: 支付人中id最小的且在职
     * @param string $key 支付人配置key
     * @return int|mixed|string
     * @date 2023/9/8
     */
    public function getPayStaff($key)
    {
        //获取支付人
        $pay_staff = EnumsService::getInstance()->getSettingEnvValueIds($key);
        $staff_repository = new HrStaffRepository();
        $user_info = [];
        if (!empty($pay_staff)) {
            sort($pay_staff);
            $staff_data = $staff_repository->getStaffListByStaffIds($pay_staff);
            $pay_staff_id = 0;
            foreach ($pay_staff as $staff_id) {
                $staff_info = $staff_data[$staff_id] ?? [];
                if (isset($staff_info['state']) && $staff_info['state'] == StaffInfoEnums::STAFF_STATE_IN && $staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO) {
                    $pay_staff_id = $staff_id;
                    break;
                }
            }
            //模拟登录人信息
            if (!empty($pay_staff_id)) {
                //获取用户信息
                $us = new UserService();
                $user_info = $us->getLoginUser($pay_staff_id);
            }
        }

        return $user_info;
    }

    /**
     * 支付凭证下载
     * @param $id
     * @return array[]
     * @throws ValidationException
     */
    public function downloadPaymentVoucher($id): array
    {
        $payment = Payment::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $id],
        ]);
        if (empty($payment)) {
            throw new ValidationException(self::$t->_('record_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        if (empty($payment->payment_voucher)) {
            throw new ValidationException(self::$t->_('record_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return ['file_url' => $payment->payment_voucher];
    }

}
