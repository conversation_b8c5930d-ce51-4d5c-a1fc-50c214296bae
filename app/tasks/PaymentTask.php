<?php

use App\Library\BaseController;
use App\Library\Enums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\FlashPayHelper;
use App\Library\RocketMQ;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentUpdateService;
use App\Modules\Pay\Services\FlashPaySftpService;
use App\Modules\Pay\Services\PayFlowService as PayPayFlowService;
use App\Modules\Pay\Services\PayService;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Pay\Services\FinalPayService;
use App\Modules\Reimbursement\Services\UpdateService;
use App\Modules\Third\Services\FlashPayService;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\ErrCode;
use App\Modules\Pay\Models\Payment;
use App\Library\Enums\PayEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\BankFlowEnums;
use App\Models\oa\PaymentFlashPayConfigModel;
use App\Modules\Pay\Models\PaymentPay;
use App\Models\oa\PaymentOnlinePayModel;
use App\Modules\User\Services\UserService;
use App\Repository\HrStaffRepository;
use App\Repository\oa\OrdinaryPaymentRepository;
use App\Repository\oa\ReimbursementRepository;


class PaymentTask extends RocketMqBaseTask
{

    /**
     * 获取支付凭证
     * @return void
     */
    public function handlePaymentVoucherAction()
    {
        $this->tq = 'payment-voucher';
        $this->fire();
    }

    /**
     * 处理消息内容
     * @param $msgBody
     * @return bool
     * @throws BusinessException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    protected function processOneMsg($msgBody)
    {
        $this->logger->info('processOneMsg ' . base64_decode($msgBody));
        $msg_data = $this->getMessageData($msgBody);
        if (empty($msg_data)) {
            return false;
        }
        return FlashPayService::getInstance()->doPaymentVoucher($msg_data['data']);
    }

    public function flush_oldAction()
    {
        $sql = 'SELECT poid,sum(total) as total,sum(pay_total) as pay_total from purchase_order_product GROUP BY poid';
        $arr = $this->db_oa->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);


        $orderIdArr = [];
        foreach ($arr as $k => $v) {
            if ($v['total'] - $v['pay_total'] == 0) {
                $orderIdArr[] = $v['poid'];
            }
        }

        $str = "purchase_order id==" . implode(",", $orderIdArr) . " update is_cite=1";
        $this->logger->info($str);
        echo $str . PHP_EOL;

        if (!empty($orderIdArr)) {
            $sql = 'update purchase_order set is_cite=1 where id in (' . implode(",", $orderIdArr) . ')';
            $this->db_oa->query($sql);
        }
    }


    public function flush_applyAction()
    {
        //线上没有apply_product_id为空的情况
        $sql = "SELECT pop.apply_product_id from purchase_order_product as pop join purchase_order as po on po.id = pop.poid where po.`status` in (1,3)";
        $arr = $this->db_oa->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC);
        $ids = array_filter(array_column($arr, "apply_product_id"));

        $str = "purchase_apply_product id=" . implode(",", $ids) . " update order_total=total";
        $this->logger->info($str);
        echo $str . PHP_EOL;

        $sql = 'update purchase_apply_product set order_total = total where id in (' . implode(",", $ids) . ')';
        $this->db_oa->query($sql);
    }


    /**
     * v6870第三期刷数据
     */

    public function flushAction()
    {
        $items = \App\Modules\Purchase\Models\PurchasePayment::find();
        if (empty($items)) {
            echo 'not found data' . PHP_EOL;
            return;
        }

        foreach ($items as $item) {
            if (!empty($item->po_id)) {
                $order = \App\Modules\Purchase\Models\PurchaseOrder::findFirst([
                    'conditions' => 'id = ?0',
                    'bind' => [$item->po_id],
                ]);

                if (!empty($order)) {
                    $item->vendor = $order->vendor;
                    $item->vendor_contact = $order->vendor_contact;
                    $item->vendor_phone = $order->vendor_phone;
                    $item->vendor_email = $order->vendor_email;
                    $item->payment_to = $order->payment_to;
                    $item->loan_time = $order->loan_time;
                }
            }

            $receipts = \App\Modules\Purchase\Models\PurchasePaymentReceipt::find(
                [
                    'conditions' => 'ppid = ?0',
                    'bind' => [$item->id],
                ]
            );

            $not_tax_amount = 0;
            $vat7_amount = 0;
            $receipt_amount = 0;
            $real_amount = 0;


            foreach ($receipts as $receipt) {
                $receipt->real_amount = bcsub($receipt->tax_total_price, $receipt->wht_amount);
                $receipt->save();

                $po = \App\Modules\Purchase\Models\PurchaseOrderProduct::findFirst(
                    [
                        'conditions' => 'id=?0',
                        'bind' => [$receipt->pop_id],
                    ]
                );
                if (!empty($po)) {
                    $receipt->product_option = $po->product_option;
                    $receipt->finance_code = $po->finance_code;
                    $receipt->save();
                }

                $receipt_amount += $receipt->ticket_amount;
                $vat7_amount += $receipt->tax_ratio;
                $not_tax_amount += $receipt->total_price;
                $real_amount += $receipt->real_amount;
            }

            $item->not_tax_amount = $not_tax_amount;
            $item->vat7_amount = $vat7_amount;
            $item->receipt_amount = $receipt_amount;
            $item->real_amount = $real_amount;

            $item->save();
        }
    }

    /**
     * 刷规格字段，和finance_code
     */
    public function flush_product_optionAction()
    {

        $order = [];

        $receipts = \App\Modules\Purchase\Models\PurchasePaymentReceipt::find();

        foreach ($receipts as $receipt) {
            if (empty($order[$receipt->pop_id])) {
                $order[$receipt->pop_id] = \App\Modules\Purchase\Models\PurchaseOrderProduct::findFirst(
                    [
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $receipt->pop_id],
                    ])->toArray();
            }

            $tmp = $order[$receipt->pop_id];
            if (empty($tmp)) {
                continue;
            }
            if ($receipt->product_option != $tmp['product_option'] || $receipt->finance_code != $tmp['finance_code']) {

                echo "purchase_payment_receipt id =" . $receipt->id . " " . $receipt->product_option . "=pop_id==" . $receipt->pop_id . "=" . $tmp['product_option'] . "  " . PHP_EOL;
                echo "purchase_payment_receipt id =" . $receipt->id . " " . $receipt->finance_code . "=pop_id==" . $receipt->pop_id . "=" . $tmp['finance_code'] . "  need update " . PHP_EOL;

                $receipt->product_option = $tmp['product_option'];
                $receipt->finance_code = $tmp['finance_code'];

                $flag = $receipt->save();
                if ($flag === false) {
                    $tArr = [];
                    $messages = $receipt->getMessages();
                    foreach ($messages as $message) {
                        $tArr[] = $message->getMessage();
                    }
                    echo "purchase_payment_receipt id =" . $receipt->id . "更新失败===";
                    echo json_encode($tArr, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                }
            } else {
                //echo "purchase_payment_receipt id =".$receipt->id." ".$receipt->product_option."==".$tmp->product_option."  not update ".PHP_EOL;
            }
        }
    }


    public function flush_approve_timeAction()
    {
        $list = \App\Modules\Purchase\Models\PurchasePayment::find(
            [
                'conditions' => ' status = ' . \App\Library\Enums::CONTRACT_STATUS_APPROVAL,
            ]
        );

        foreach ($list as $item) {
            $request = \App\Modules\Workflow\Models\WorkflowRequestModel::findFirst(
                [
                    'conditions' => 'biz_type=' . \App\Library\Enums::WF_PURCHASE_PAYMENT . ' and biz_value=' . $item->id,
                ]
            );

            if (!empty($request)) {
                $item->approve_at = $request->approved_at;
                $item->save();
            }
        }
    }


    public function flush_whtAction($params)
    {
        $ppno_str = $params[0];
        $is_check = $params[1] ?? 1;
        $ppnoIdArr = explode(",", $ppno_str);

        if (empty($ppnoIdArr)) {
            echo "params empty" . PHP_EOL;
            return;
        }

        $list = \App\Modules\Purchase\Models\PurchasePayment::find(
            [
                'conditions' => 'ppno in ({ppnoIds:array})',
                'bind' => ['ppnoIds' => $ppnoIdArr],
            ]
        );

        \App\Library\BaseService::setLanguage('zh-CN');
        $service = \App\Modules\Purchase\Services\PaymentService::getInstance();

        //含税金额，不含税总价,vat7金额，wht金额，发票金额，实付金额
        $updateField = ['amount', 'not_tax_amount', 'vat7_amount', 'wht_amount', 'receipt_amount', 'real_amount'];

        foreach ($list as $item) {
            $data = [];
            //前端展示的数据，不乘以1000的数
            $data = $service->getDetail($item->id);
            $tmp = [];
            $tmp = $data['data'];
            if (!empty($tmp['receipt_v1'])) {
                $tmp['receipt'] = $tmp['receipt_v1'];
            }
            //处理后，乘以1000，保存
            $service->handleProductData($tmp);
            $is_update = 0;
            foreach ($updateField as $field) {
                if ($item->$field != $tmp[$field]) {
                    echo 'ppno==before===' . $item->$field . "===after===" . $tmp[$field] . PHP_EOL;
                    $item->$field = $tmp[$field];
                    $is_update = 1;
                }
            }
            if ($is_update && !$is_check) {
                $flag = $item->save();
                if ($flag !== true) {
                    echo $item->ppno . "===save==error" . var_export($flag) . PHP_EOL;
                } else {
                    echo $item->ppno . "===save==success" . var_export($flag) . PHP_EOL;
                }
            }
            unset($tmp);
        }
    }

    public function fix_amountAction($params)
    {
        $is_check = $params[0] ?? 1;

        /*$amount = 'PAR202102110002,PAR202102160002,PAR202103060050,PAR202103110029,PAR202103160042';

        $not_tax_amount = 'PAR202102110002,PAR202102160002,PAR202103060050,PAR202103110029,PAR202103160042';

        $vat7_amount = 'PAR202102110002,PAR202102160002,PAR202103060050,PAR202103110029,PAR202103160042';

        $wht_amount = 'PAR202102160002,PAR202103110029,PAR202103190035,PAR202104070003,PAR202104070012,PAR202104190031,PAR202105240024,PAR202105240019';

        $receipt_amount = 'PAR202102110002,PAR202102160002,PAR202103060050,PAR202103110029,PAR202103160042';

        $real_amount = 'PAR202102010006,PAR202102110002,PAR202102160002,PAR202103060050,PAR202103110029,PAR202103160042,PAR202103190035,PAR202104070003,PAR202104070012,PAR202104190031,PAR202105240024,PAR202105240019';


        $arr = ['amount','not_tax_amount','vat7_amount','wht_amount','receipt_amount','real_amount'];*/

        $str = 'PAR202103110029,PAR202103190035,PAR202104070003,PAR202104070012,PAR202104190031,PAR202105240024,PAR202105240019';
        //$data = explode(",",$str);
        //$data = [];

        /*foreach ($arr as $key){
            $data = array_merge($data,explode(",",$$key));
        }*/


        $this->flush_whtAction([$str, $is_check]);

    }


    public function flush_order_totalAction()
    {
        $receipts = \App\Modules\Purchase\Models\PurchasePaymentReceipt::find();

        $orders = \App\Modules\Purchase\Models\PurchaseOrderProduct::find(
            [
                'conditions' => 'id in ({ids:array})',
                'bind' => ['ids' => array_column($receipts->toArray(), 'pop_id')],
            ]
        )->toArray();

        $popIdToTotal = array_column($orders, "total", 'id');


        foreach ($receipts as $receipt) {
            echo 'id==' . $receipt->id . "===pop_id=" . $receipt->pop_id . "===order_total===" . $popIdToTotal[$receipt->pop_id] . PHP_EOL;
            $receipt->order_total = $popIdToTotal[$receipt->pop_id] ?? 0;
            $flag = $receipt->save();
            $message_str = '';
            if ($flag === false) {
                $messages = $receipt->getMessages();
                foreach ($messages as $message) {
                    echo $message . PHP_EOL;
                }
                echo PHP_EOL;
            }
            echo 'id==' . $receipt->id . "===pop_id=" . $receipt->pop_id . "===order_total===" . $popIdToTotal[$receipt->pop_id] . "===res==" . json_encode($flag) . PHP_EOL;
        }
    }


    /**
     * @Desc: 【14829】支付数据自动超时结束任务
     * @author: W_uniQue
     * @Time: 2022/10/24 10:25
     */
    public function auto_overtime_withdrawAction()
    {
        try {
            PayService::getInstance()->autoOvertimeWithdraw();
            echo '执行完成';
        } catch (ValidationException $e) {
            echo '执行终止：' . $e->getMessage();
            $this->logger->info($e->getMessage());
        } catch (Exception $e) {
            echo '执行异常：' . $e->getMessage();
            $this->logger->warning($e->getMessage());
        }
    }

    /**
     * 针对代理支付-马来、菲律宾；
     * 一级支付人驳回、三级支付人驳回-支付失败；
     * 修改业务模块支付状态为未支付
     */
    public function auto_agency_payment_withdrawAction()
    {
        // 进程加原子锁
        $this->checkLock(__METHOD__);
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        try {
            //获取未支付的数据
            $payment_list = Payment::find([
                'conditions' => 'pay_status = :pay_status: and oa_type = :oa_type:',
                'bind' => ['pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_FAILED, 'oa_type' => BankFlowEnums::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT],
            ])->toArray();
            $log .= '本次共捞取到符合条件的总数为：' . count($payment_list) . PHP_EOL;
            if ($payment_list) {
                $staff_ids = array_values(array_unique(array_column($payment_list, 'apply_staff_id')));
                //获取员工数据
                $staff_lists = (new UserService())->getUserListByStaffIds($staff_ids);
                $user = [];
                foreach ($staff_lists as $staff) {
                    //字段特殊处理，满足撤回接口用户参数要求
                    $user[$staff->staff_info_id] = [
                        'id'         => $staff->staff_info_id,
                        'name'       => $staff->name,
                        'nick_name'  => $staff->nick_name ?? '',
                        'email'      => $staff->email ?? '',
                        'mobile'     => $staff->mobile ?? '',
                        'job_title'  => $staff->getJobTitle()->job_name ?? '',
                        'department' => $staff->getDepartment()->name ?? '',
                        'state'      => $staff->state,
                    ];
                }

                foreach ($payment_list as $pay) {
                    try {
                        if (!isset($user[$pay['apply_staff_id']])) {
                            throw new Exception("用户数据查询为空【{$pay['apply_staff_id']}】", ErrCode::$SYSTEM_ERROR);
                        }

                        //撤回操作
                        $res = PayService::getInstance()->withdraw($pay['id'], $pay['not_pay_reason'], $user[$pay['apply_staff_id']]);
                        if (!$res) {
                            throw new Exception('withdraw return false');
                        }
                        $log .= "【{$pay['id']}】支付数据撤销成功。" . PHP_EOL;
                    } catch (Exception $e) {
                        $log .= "【{$pay['id']}】，【{$pay['apply_staff_id']}】，【{$pay['updated_at']}】支付数据撤销失败。" . PHP_EOL;
                        $this->logger->error("auto_agency_payment_withdraw-failed:【{$pay['id']}】" . $e->getMessage());
                    }
                }
            }
        } catch (Exception $e) {
            $log    .= 'Exception message=' . $e->getMessage() . 'trace=' . $e->getTraceAsString() . PHP_EOL;
            $this->logger->notice('针对代理支付-一级支付人驳回、三级支付人驳回-支付失败；修改业务模块支付状态为未支付 Exception log : ' . $log);
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info('针对代理支付-一级支付人驳回、三级支付人驳回-支付失败；修改业务模块支付状态为未支付 success log : ' . $log);
        // 释放进程锁
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 支付模块-在线支付/我的支付-银行支付-在线支付-下载
     * 每5分钟执行一次
     */
    public function online_exportAction()
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            // 1.提取待处理的下载任务(最早入队的)
            $task_model = DownloadCenterService::getInstance()->getFirstPendingTask(DownloadCenterEnum::PAYMENT_ONLINE);
            if (empty($task_model)) {
                throw new ValidationException('没有待下载的任务', ErrCode::$VALIDATE_ERROR);
            }

            // 2.1解析参数
            $params = !empty($task_model->args_json) ? json_decode(base64_decode($task_model->args_json, true), true) : [];
            $params['language'] = $params['language'] ?? DownloadCenterEnum::DETAUL_LANGUAGE;

            $log .= '任务ID: ' . $task_model->id . PHP_EOL;
            $log .= '任务参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            // 2.2获取数据总量
            $all_total = FinalPayService::getInstance()->getExportOnlineListCount($params);
            $total_page_num = ceil($all_total / 2000);//由于导出要从审批日志获取一级、二级支付人信息组，所以担心一下子查1w条数据告警，所以按照2k查询
            $log .= '预计总量: ' . $all_total . PHP_EOL;
            $log .= '预计批次: ' . $total_page_num . PHP_EOL;

            // 2.3设置系统语言
            FinalPayService::setLanguage($params['language']);

            // 2.4分批取数
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $log .= '----- 取数批次: ' . $page_num . '-----' . PHP_EOL;

                $params['pageNum'] = $page_num;
                $params['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list = FinalPayService::getInstance()->getExportOnlineList($params);

                // 2.5合并数据
                foreach ($list['data'] as $item) {
                    $excel_data[] = array_values($item);
                }
                $log .= '本批数量: ' . count($list['data']) . PHP_EOL;

                unset($list);

                $log .= '当前内存: ' . memory_usage() . PHP_EOL;
            }

            $log .= '实际总量: ' . count($excel_data) . PHP_EOL;
            $log .= '当前内存: ' . memory_usage() . PHP_EOL;

            // 2.6获取Excel表头
            $excel_header = FinalPayService::getInstance()->getExportExcelHeaderFields();

            // 2.7生成Excel
            $excel_result = FinalPayService::getInstance()->exportExcel($excel_header, $excel_data, $task_model->file_name);
            $log .= 'Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            unset($excel_data);

            // 3.更新任务
            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $save_model_result = DownloadCenterService::getInstance()->saveTask($task_model, DownloadCenterEnum::TASK_STATUS_SUCCESS, $excel_result['data']);
                if ($save_model_result === false) {
                    $log .= '任务状态: 更新失败, 原因可能是: ' . get_data_object_error_msg($task_model) . PHP_EOL;
                } else {
                    $log .= '任务状态: 更新成功' . PHP_EOL;
                }
            } else {
                // 记录错误日志
                $log .= '任务状态: 未更新, 生成Excel失败, 待下次重新执行' . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('支付模块-在线支付-导出任务: ' . $log);
        } else {
            $this->logger->info('支付模块-在线支付-导出任务: ' . $log);
        }

        exit($log);
    }


    /**
     * @description: 每天8点，FlashPay超时未反馈数据
     * @param $params
     * @author: AI
     * @date: 2025-01-13
     */
    public function flashPaySftpResultOvertimeAction($params)
    {
        $this->logger->info('FlashPay SFTP 超时未反馈数据告警任务开始执行');
        $date_at             = $params[0] ?? date('Y-m-d', strtotime('-1 day'));
        $result              = FlashPaySftpService::getInstance()->getPendingResultBatchesByDate($date_at);
        if (!empty($result)) {
            $message = json_encode(['超时未反馈数据' => $result], JSON_UNESCAPED_UNICODE);
            FlashPayHelper::sendNotice($message);
        }
        $this->logger->info('FlashPay SFTP 超时未反馈数据告警任务结束');

    }

    /**
     * 获取支付凭证
     * @param $params
     * @return void
     * @throws BusinessException
     * @throws \GuzzleHttp\Exception\GuzzleException
     *
     */
    public function batchGetPaymentVoucherAction($params)
    {
        $this->logger->info('FlashPay 获取支付凭证任务开始执行');
        $out_trade_no = $params[0] ?? '';
        FlashPayService::getInstance()->batchDoPaymentVoucher($out_trade_no);
        $this->logger->info('FlashPay 获取支付凭证任务结束');

    }


    /**
     * @description: 每小时执行一次，获取FlashPay SFTP支付结果
     * @param $params
     * @throws BusinessException
     * @throws ValidationException
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @author: AI
     * @date: 2025-01-13
     */
    public function flashPaySftpResultAction($params)
    {
        self::setLanguage(get_country_code());
        $this->logger->info('FlashPay SFTP支付结果获取任务开始执行');
        $pay_bank_account = $params[0] ?? '';
        $result = FlashPaySftpService::getInstance()->getFlashPaySftpResults($pay_bank_account);
        echo json_encode($result,JSON_UNESCAPED_UNICODE);
        $this->logger->info('FlashPay SFTP支付结果获取任务结束');

    }

    /**
     * 每天9点10分
     * FlashPay SFTP打款文件处理
     * 处理flash_pay_method为2的支付数据，生成Excel文件并通过SFTP上传
     *
     * @description: 处理FlashPay SFTP方式的打款文件生成和上传
     * @author: AI
     * @date: 2024-12-19
     *
     * 每天执行一次
     * @param $params
     * @return void
     * @throws BusinessException
     */
    public function flashPaySftpAction($params)
    {
        self::setLanguage(get_country_code());
        $this->logger->info('FlashPay SFTP发起支付任务开始执行');
        // 进程加原子锁
        $this->checkLock(__METHOD__, 10800);
        $date_at         = $params[0] ?? date('Y-m-d');
        $pay_bank_account = $params[1] ?? '';
        try {
            FlashPaySftpService::getInstance()->processFlashPaySftp($date_at, $pay_bank_account);
        } catch (Exception $e) {
            $this->clearLock(__METHOD__);
            throw $e;
        }        // 释放进程锁
        $this->clearLock(__METHOD__);
        $this->logger->info('FlashPay SFTP发起支付任务结束');

    }

    /**
     * FlashPay在线支付
     * 注意: 需串行, 注意单据处理的幂等, 务必防止单据重抛
     *
     * 每5分钟执行一次
     */
    public function flashpay_onlineAction()
    {
        // 进程加原子锁
        $this->checkLock(__METHOD__, 10800);

        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $log .= '初始内存: ' . memory_usage() . PHP_EOL;
        $is_exception = false;

        try {
            //在线支付 && pay支付中 && 未传输
            $payment = Payment::find([
                'conditions' => 'is_online_pay = :is_online_pay: and pay_status = :pay_status: and out_send_status = :out_send_status: and flash_pay_method = :flash_pay_method:',
                'bind' => [
                    'is_online_pay' => PayEnums::IS_ONLINE_PAY_YES,
                    'pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING,
                    'out_send_status' => PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_NO,
                    'flash_pay_method'=> PayEnums::PAYMENT_MODULE_FLASH_PAY_METHOD_API,
                ],
            ]);
            $payment_list = $payment->toArray();
            $log .= '本次共捞取到符合条件的总数为：' . count($payment_list) . PHP_EOL;
            if (!empty($payment_list)) {
                $success_payment_ids = $error_payment_ids = [];
                //设置语种
                self::setLanguage(get_country_code());

                //获取收款人的银行和FLASH PAY的银行对照码，直接不发送请求即可
                $flash_pay_bank = PayService::getInstance()->getFlashPayBank();
                if (empty($flash_pay_bank)) {
                    throw new ValidationException(static::$t->_('payment_pay_flash_pay_bank'), ErrCode::$VALIDATE_ERROR);
                }

                //获取支付单据中费用所属公司配置
                $pay_bank_account = array_values(array_unique(array_filter(array_column($payment_list, 'pay_bank_account'))));
                $flash_pay_config = PaymentFlashPayConfigModel::find([
                    'columns' => 'app_key,merchant_private_key,flashpay_public_key,cost_company_id,flashpay_sftp_shopname',
                    'conditions' => 'flashpay_sftp_shopname in ({flashpay_sftp_shopname:array})',
                    'bind' => ['flashpay_sftp_shopname' => $pay_bank_account],
                ])->toArray();

                //若不存在支付配置，直接不发送请求即可
                if (empty($flash_pay_config)) {
                    throw new ValidationException(static::$t->_('payment_pay_online_pay_config_error'), ErrCode::$VALIDATE_ERROR);
                }
                $flash_pay_config = array_column($flash_pay_config, null, 'flashpay_sftp_shopname');
                foreach ($payment as $key => $item) {
                    if ($key % 500 == 0) {
                        sleep(1);
                    }

                    //先把单子改为待反馈，防止重复支付
                    $one_payment_info_log = '单据：' . $item->no . ' 处理开始：' . PHP_EOL;
                    try {
                        $payment_online_pay = FlashPayService::getInstance()->savePayDb(PayEnums::PAYMENT_MODULE_FLASH_PAY_METHOD_API,$item->id);
                        //调取pay的代付接口
                        $this->handleFlashPay($item, $payment_online_pay, $flash_pay_bank, $flash_pay_config, $one_payment_info_log);
                        $success_payment_ids[] = $item->id;
                        $one_payment_info_log .= '处理结束，处理结果为成功' . PHP_EOL;
                        $this->logger->info($one_payment_info_log);
                    } catch (Exception $e) {
                        $error_payment_ids[] = $item->id;;
                        $one_payment_info_log .= '处理结束，处理结果为异常，原因是：' . $e->getMessage() . PHP_EOL;
                        $this->logger->notice($one_payment_info_log);
                        continue;
                    }
                }
                $log .= '本次处理成功总数为：' . count($success_payment_ids) . '条，涉及的单据ID组为：' . json_encode($success_payment_ids, JSON_UNESCAPED_UNICODE) . PHP_EOL;
                $log .= '本次处理失败总数为：' . count($error_payment_ids) . '条，涉及的单据ID组为：' . json_encode($error_payment_ids, JSON_UNESCAPED_UNICODE) . PHP_EOL;
            } else {
                $log .= 'payment flashpay no data need handle' . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }

        $log .= '当前内存: ' . memory_usage() . PHP_EOL;
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('支付模块-FlashPay在线支付-任务: ' . $log);
        } else {
            $this->logger->info('支付模块-FlashPay在线支付-任务: ' . $log);
        }

        // 释放进程锁
        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * @param object $item 支付单据信息组
     * @param object $payment_online_pay 支付单据流水日志组
     * @param array $flash_pay_bank 收款人的银行和FLASH PAY的银行对照码组
     * @param array $flash_pay_config 支付单据中各费用所属公司配置组
     * @param string $log 日志内容
     * @throws BusinessException
     * @throws ValidationException
     */
    private function handleFlashPay($item, $payment_online_pay, $flash_pay_bank, $flash_pay_config, &$log)
    {
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //查询库里是否已被其他任务执行
            $payment = Payment::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $item->id],
                'for_update' => true,
            ]);

            //未找到或者已被其它任务已执行
            if (empty($payment) || $payment->out_send_status != PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_WAIT) {
                $repeat_log = '待反馈且发起调取pay代付接口前判断捞取的支付单据是否已被其它任务已执行： ' . (!empty($payment) ? $payment->no : '或未找到') . '直接跳过' . PHP_EOL;
                throw new ValidationException($repeat_log, ErrCode::$VALIDATE_ERROR);
            }

            if ($payment->flash_pay_method != PayEnums::PAYMENT_MODULE_FLASH_PAY_METHOD_API) {
                throw new ValidationException('非api支付数据 ' . $payment->no, ErrCode::$VALIDATE_ERROR);
            }

            //某个单据的费用所属公司为空或者没有在线支付配置信息
            if (empty($payment->pay_bank_account) || !isset($flash_pay_config[$payment->pay_bank_account])) {
                throw new ValidationException($payment->no . ' pay_bank_account[' . $payment->pay_bank_account . '] not found config', ErrCode::$VALIDATE_ERROR);
            }

            //在线支付的单据，单据的收款信息的银行都是一样的，所以获取单据下的一笔支付信息即可
            $payment_pay = PaymentPay::findFirst([
                'columns' => 'bank_name,bank_account,bank_account_name',
                'conditions' => 'payment_id = :payment_id:',
                'bind' => ['payment_id' => $payment->id],
            ]);
            if (empty($payment_pay)) {
                throw new ValidationException($payment->no . ' payment_pay not found', ErrCode::$VALIDATE_ERROR);
            }

            //校验银行码是否配置完整
            if (!isset($flash_pay_bank[$payment_pay->bank_name])) {
                throw new ValidationException($payment->no . ' ' . static::$t->_('payment_pay_flash_pay_bank'), ErrCode::$VALIDATE_ERROR);
            }

            //每笔单据交易流水号
            $out_trade_no = $payment_online_pay->out_trade_no;
            //组装pay代付接口请求参数
            $flash_pay_create_order = [
                'outTradeNo' => $out_trade_no,//商户订单号（单据号+随机10位流水号）
                'outTradeTime' => gmdate_customize_by_datetime($payment->updated_at),//商户订单时间（第2级支付人点击提交的时间（updated_at），payment表中这个字段是0时区存储的，所以需要换算成业务时间传递给pay）
                'amount' => intval(bcmul($payment->amount_total_actually, 100)),//交易金额（实付金额总计*100）
                'cur' => static::$t->_(GlobalEnums::$currency_item[$payment->currency]),//币种，泰国：THB ，马来西亚：MYR
                'subject' => static::$t->_(BankFlowEnums::$oa_type_id_to_lang_key[$payment->oa_type]),//订单标题（模块翻译）
                'body' => $payment->apply_staff_id,//订单描述
                'notes' => 'pay ' . $payment->no,//转账备注
                'extNotes' => $payment->no,//附加信息
                'outBizNo' => $payment->no,//新增商户业务单号用于验重
                'notifyUrl' => env('base_url') . '/third/flash_pay/payTradeSync',//异步回调地址
                'payeeAccountNo' => $payment_pay->bank_account,//收款人账户号（payment_pay表bank_account）
                'payeeAccountName' => $payment_pay->bank_account_name,//账户名（payment_pay表bank_account_name）
                'payeeAccountType' => 1,//收款账户类型（0：钱包1：银行账户 2：银行卡号）
                'payeeBankCode' => $flash_pay_bank[$payment_pay->bank_name],//收款银行编码（拿payment_pay表bank_name去setting_env中code='payment_flash_pay_bank'配置中找到对应的pay银行编码）
                'timeliness' => 0,//时效性（0：实时，1：非实时）
            ];

            //调取pay代付接口
            $pay_result = FlashPayService::getInstance()->transfer($flash_pay_create_order, $flash_pay_config[$payment->pay_bank_account]);
            $code = $pay_result['code'];
            $flash_pay_data = $pay_result['data'] ?? [];
            $trade_no = '';//pay交易号
            $trade_time = null;//pay交易时间
            if ($flash_pay_data) {
                $trade_no = $flash_pay_data['tradeNo'] ?? '';
                $trade_time = $flash_pay_data['tradeTime'] ?? null;
            }

            //代付接口请求后-开始记录pay返回的交易信息到payment表
            if ($code == 0) {
                //确认成功
                $payment->out_send_status = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_WAIT;//发送状态待反馈
            } else if ($code > 0 && $code < 999000) {
                //确认失败
                $payment->out_send_status = PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_FAILED;//发送状态失败
                $payment->pay_status = PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED;//pay支付失败
            } else {
                //未知原因的失败
                $this->logger->warning($payment->no . ' pay transfer unknown failed reason, outTradeNo [' . $out_trade_no . ']');
            }
            //获取pay在线支付人
            $payer_id = FlashPayService::getInstance()->getPayer();
            $now_time = time();
            $business_time = date('Y-m-d H:i:s', $now_time);//业务时间
            $zero_time = gmdate('Y-m-d H:i:s', $now_time);//0时区
            $payment->payer_id = $payer_id;//最终支付人
            $payment->payer_date = $zero_time;//最终支付时间需要0时区存储
            $payment->out_send_at = $business_time;//发送时间
            $payment->out_trade_code = $code;//pay返回的code
            $payment->out_trade_no = $trade_no;
            $payment->out_trade_at = $trade_time;
            $payment->updated_at = $zero_time;//更新时间需要0时区存储
            $bool = $payment->save();
            if ($bool === false) {
                throw new BusinessException($payment->no . ' save flash pay info error,data:' . json_encode($payment->toArray(), JSON_UNESCAPED_UNICODE) . ';可能存在的原因是：' . get_data_object_error_msg($payment), ErrCode::$BUSINESS_ERROR);
            }

            //pay支付失败的单据，需要驳回到一级审批人，重新走支付审批
            if ($payment->pay_status == PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED) {
                FlashPayService::getInstance()->reject($payment, $payer_id);
            }

            //记录支付模块关联在线支付交易流水
            $payment_online_pay->trade_no = $trade_no;
            $payment_online_pay->trade_time = $trade_time;
            $payment_online_pay->payment_amount = $flash_pay_data['paymentAmount'] ?? 0;
            $payment_online_pay->cur = $flash_pay_data['cur'] ?? '';
            $payment_online_pay->code = $code;
            $payment_online_pay->updated_at = $business_time;
            $bool = $payment_online_pay->save();
            if ($bool === false) {
                throw new BusinessException($payment->no . ' payment online pay error,data:' . json_encode($payment_online_pay->toArray(), JSON_UNESCAPED_UNICODE) . ';可能存在的原因是：' . get_data_object_error_msg($payment_online_pay), ErrCode::$BUSINESS_ERROR);
            }
            $log .= $payment->no . ($code == 0 ? ' pay success' : ' pay failed') . PHP_EOL;
            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            throw $e;
        } catch (BusinessException $e) {
            $db->rollback();
            throw $e;
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
    }

    /**
     * FlashPay在线支付待付款、支付中的订单同步PAY那边的交易状态
     * 每30分钟执行一次
     */
    public function sync_flashpay_trade_statusAction()
    {
        $this->checkLock(__METHOD__);
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $is_exception = false;

        try {
            //在线支付 && pay支付中 && 待反馈 && 交易待支付、交易处理中
            $payment = Payment::find([
                'conditions' => 'is_online_pay = :is_online_pay: and pay_status = :pay_status: and out_send_status = :out_send_status: and out_trade_status in ({out_trade_status:array}) and out_trade_no != :out_trade_no: and flash_pay_method = :flash_pay_method:',
                'bind' => [
                    'is_online_pay' => PayEnums::IS_ONLINE_PAY_YES,
                    'pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING,
                    'out_send_status' => PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_WAIT,
                    'out_trade_no' => '',
                    'out_trade_status' => [
                        PayEnums::PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_WAIT,
                        PayEnums::PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_ING,
                    ],
                    'flash_pay_method'=> PayEnums::PAYMENT_MODULE_FLASH_PAY_METHOD_API,
                ],
            ]);
            $list = $payment->toArray();
            if (!empty($list)) {
                //获取支付单据中费用所属公司配置
                $cost_company_id = array_values(array_unique(array_filter(array_column($list, 'cost_company_id'))));
                $flash_pay_config = PaymentFlashPayConfigModel::find([
                    'columns' => 'app_key,merchant_private_key,flashpay_public_key,cost_company_id',
                    'conditions' => 'cost_company_id in ({cost_company_id:array})',
                    'bind' => ['cost_company_id' => $cost_company_id],
                ])->toArray();

                //若不存在支付配置，直接不发送请求即可
                if (empty($flash_pay_config)) {
                    throw new ValidationException(static::$t->_('payment_pay_online_pay_config_error'), ErrCode::$VALIDATE_ERROR);
                }
                $flash_pay_config = array_column($flash_pay_config, null, 'cost_company_id');
                self::setLanguage('th');
                foreach ($payment as $item) {
                    //某个单据的费用所属公司为空或者没有在线支付配置信息
                    if (empty($item->cost_company_id) || !isset($flash_pay_config[$item->cost_company_id])) {
                        throw new ValidationException($item->no . ' cost_company_id[' . $item->cost_company_id . '] not found config', ErrCode::$VALIDATE_ERROR);
                    }
                    //由于脚本处理过程中，回调已经被执行了，单据状态已经发生了变化，故而再次查询下单据看是否是pay支付中，不是，则不需要处理，直接跳过
                    $one_payment_info = FlashPayService::getInstance()->getPaymentInfo($item->out_trade_no);
                    if ($one_payment_info && $one_payment_info->pay_status != PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_ING) {
                        continue;
                    }

                    try {
                        FlashPayService::getInstance()->syncFlashPayTradeStatus($item, $flash_pay_config[$item->cost_company_id]);
                        $log .= '申请单号：【' . $item->no . '】同步FlashPay交易状态到订单状态成功' . PHP_EOL;
                    } catch (ValidationException $e) {
                        $log .= '申请单号：【' . $item->no . '】同步FlashPay交易状态到订单状态失败，原因是：' . $e->getMessage() . PHP_EOL;
                        $this->logger->notice('申请单号：【' . $item->no . '】同步FlashPay交易状态到订单状态失败，原因是：' . $e->getMessage());
                    } catch (Exception $e) {
                        $log .= '申请单号：【' . $item->no . '】同步FlashPay交易状态到订单状态失败，原因是：' . $e->getMessage() . PHP_EOL;
                        $this->logger->warning('申请单号：【' . $item->no . '】同步FlashPay交易状态到订单状态失败，原因是：' . $e->getMessage());
                    }
                }
            } else {
                $log .= 'payment sync_flashpay_trade_status no data need handle' . PHP_EOL;
            }
        } catch (ValidationException $e) {
            $log .= $e->getMessage() . PHP_EOL;
        } catch (Exception $e) {
            $log .= $e->getMessage() . PHP_EOL;
            $is_exception = true;
        }
        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

        if ($is_exception) {
            $this->logger->error('FlashPay在线支付待付款、支付中的订单同步PAY那边的交易状态任务: ' . $log);
        } else {
            $this->logger->info($log);
        }

        $this->clearLock(__METHOD__);
        exit($log);
    }

    /**
     * 自动取消超时待支付单据
     * 取数逻辑: 审批通过,待支付状态的业务单,且审批通过时间早于指定配置
     *
     * 处理临时指定配置的单据: 手动执行
     * php app/cli.php payment auto_cancel_payment tmp
     *
     * @param array $args 外部参数
     */
    public function auto_cancel_paymentAction(array $args = [])
    {
        $process_name = str_replace('Action', '', __FUNCTION__);
        $log = '任务名称: ' . $process_name . PHP_EOL;
        $log .= '开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->check_process($process_name);
        try {
            // 任务类型, 如果是临时的, 处理的数据则从setting_env中取
            $task_type = $args[0] ?? 'timeout';

            $log .= "任务类型: {$task_type}" . PHP_EOL;

            //设置语言
            self::setLanguage('zh');

            // 初始化变量
            $reimbursement_success = $reimbursement_error = $ordinary_success = $ordinary_error = 0;
            $reimbursement_error_msg = '';
            $ordinary_error_msg = '';
            $pending_reimbursement_orders = [];
            $pending_ordinary_orders = [];

            // 临时指定的方式
            if ($task_type == 'tmp') {
                // 1. 获取报销要处理的指定单据
                $reimbursement_order_nos = EnumsService::getInstance()->getSettingEnvValueIds('auto_cancel_pay_reimbursement_orders');
                $reimbursement_order_nos = array_unique(array_filter($reimbursement_order_nos));
                $log .= '已配置的待处理报销单号, 共 ' . count($reimbursement_order_nos) . ' 个, ' . implode(',', $reimbursement_order_nos) . PHP_EOL;

                $pending_reimbursement_orders = ReimbursementRepository::getInstance()->getPendingPayListByNos($reimbursement_order_nos);
                $pending_reimbursement_orders_nos = array_column($pending_reimbursement_orders, 'no');
                $this->logger->info('符合条件的待处理报销单号, 共 ' . count($pending_reimbursement_orders_nos) . ' 个,' . implode(',', $pending_reimbursement_orders_nos));

                // 不符合条件的报销数据
                $diff_reimbursement_order_nos = array_diff($reimbursement_order_nos, $pending_reimbursement_orders_nos);
                $log .= '不符合条件的待处理报销单号(无数据/未审批通过/非待支付), 共 ' . count($diff_reimbursement_order_nos) . ' 个,' . implode(',', $diff_reimbursement_order_nos) . PHP_EOL . PHP_EOL;


                // 2. 获取普通付款要处理的指定单据
                $ordinary_order_nos = EnumsService::getInstance()->getSettingEnvValueIds('auto_cancel_pay_ordinary_payment_orders');
                $ordinary_order_nos = array_unique(array_filter($ordinary_order_nos));
                $log .= '已配置的待处理普通付款单号, 共 ' . count($ordinary_order_nos) . ' 个,' . implode(',', $ordinary_order_nos) . PHP_EOL;

                $pending_ordinary_orders = OrdinaryPaymentRepository::getInstance()->getPendingPayListByNos($ordinary_order_nos);
                $pending_ordinary_orders_nos = array_column($pending_ordinary_orders, 'apply_no');
                $this->logger->info('符合条件的待处理普通付款单号, 共 ' . count($pending_ordinary_orders_nos) . ' 个,' . implode(',', $pending_ordinary_orders_nos));

                // 不符合条件的普通付款数据
                $diff_ordinary_order_nos = array_diff($ordinary_order_nos, $pending_ordinary_orders_nos);
                $log .= '不符合条件的待处理普通付款单号(无数据/未审批通过/非待支付), 共 ' . count($diff_ordinary_order_nos) . ' 个,' . implode(',', $diff_ordinary_order_nos) . PHP_EOL;

            } else {
                // 已超时未支付的
                // 查询符合条件的报销数据
                $time_out_number_reimbursement = EnumsService::getInstance()->getSettingEnvValue('sys_module_reimbursement_pay_timeout_day', 0);
                if (is_numeric($time_out_number_reimbursement) && $time_out_number_reimbursement > 60) {
                    $time_out_day = date('Y-m-d', strtotime("- {$time_out_number_reimbursement} days"));
                    $pending_reimbursement_orders = ReimbursementRepository::getInstance()->getTimeOutData($time_out_day);
                    $this->logger->info('本次执行报销的超时天数=' . $time_out_number_reimbursement . '; 时间=' . $time_out_day);
                    $this->logger->info('需要处理的报销单据=' . json_encode(array_column($pending_reimbursement_orders, 'no')));
                }

                // 查询符合条件的普通付款数据
                $time_out_number_ordinary = EnumsService::getInstance()->getSettingEnvValue('sys_module_ordinary_payment_pay_timeout_day', 0);
                if (is_numeric($time_out_number_ordinary) && $time_out_number_ordinary > 60) {
                    $time_out_day = date('Y-m-d', strtotime("- {$time_out_number_ordinary} days"));
                    $pending_ordinary_orders = OrdinaryPaymentRepository::getInstance()->getTimeOutData($time_out_day);
                    $this->logger->info('本次执行普通付款的超时天数=' . $time_out_number_ordinary . '; 时间=' . $time_out_day);
                    $this->logger->info('需要处理的普通付款单据=' . json_encode(array_column($pending_ordinary_orders, 'apply_no')));
                }
            }

            // 报销数据待处理
            if (!empty($pending_reimbursement_orders)) {
                //报销未进入支付模块的支付人
                $pay_user = FinalPayService::getInstance()->getPayStaff('reimbursement_pay_staff_id');
                $remark = static::$t->_('payment_auto_cancel_remark');
                $pay_data = [
                    'pay_status' => Enums::LOAN_PAY_STATUS_NOTPAY,
                    'remark' => $remark,
                ];

                //支付模块service
                $pay_service = new PayPayFlowService();
                foreach ($pending_reimbursement_orders as $k => $v) {
                    $pay_result = null;
                    if ($v['is_pay_module'] == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
                        $pay_result = $pay_service->autoNonPay($v['no'], $remark);
                    } else {
                        //未进入支付模块的单据
                        if (empty($pay_user)) {
                            $this->logger->info('报销支付失败,没有支付人, 业务单号=' . $v['no']);
                            $reimbursement_error += 1;
                            continue;
                        }

                        $pay_result = UpdateService::getInstance()->pay($v['id'], $pay_data, $pay_user);
                    }

                    if (!isset($pay_result['code']) || $pay_result['code'] != ErrCode::$SUCCESS) {
                        $this->logger->notice('报销支付失败, 业务单号=' . $v['no'] . '; 返回信息' . json_encode($pay_result, JSON_UNESCAPED_UNICODE));
                        $reimbursement_error += 1;
                        $reimbursement_error_msg .= "no={$v['no']}, message={$pay_result['message']}; ";
                    } else {
                        $this->logger->info('报销支付成功, 业务单号=' . $v['no']);
                        $reimbursement_success += 1;
                    }
                }
            }

            // 普通付款待处理的
            if (!empty($pending_ordinary_orders)) {
                //普通付款未进入支付的支付人
                $pay_user = FinalPayService::getInstance()->getPayStaff('ordinary_payment_pay_staff_id');
                $remark = static::$t->_('payment_auto_cancel_remark');
                $pay_data = [
                    'is_pay' => Enums::LOAN_PAY_STATUS_NOTPAY,
                    'remark' => $remark,
                ];

                //支付模块service
                $pay_service = new PayPayFlowService();
                foreach ($pending_ordinary_orders as $ordinary_v) {
                    $pay_result = null;
                    if ($ordinary_v['is_pay_module'] == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
                        $pay_result = $pay_service->autoNonPay($ordinary_v['apply_no'], $remark);
                    } else {
                        //未进入支付模块的单据
                        if (empty($pay_user)) {
                            $this->logger->info('普通付款支付失败,没有支付人, 业务单号=' . $ordinary_v['apply_no']);
                            $ordinary_error += 1;
                            continue;
                        }
                        $pay_data['id'] = $ordinary_v['id'];
                        $pay_result = OrdinaryPaymentUpdateService::getInstance()->pay($pay_data, $pay_user);
                    }

                    if (!isset($pay_result['code']) || $pay_result['code'] != ErrCode::$SUCCESS) {
                        $this->logger->notice('普通付款支付失败, 业务单号=' . $ordinary_v['apply_no'] . '; 返回信息' . json_encode($pay_result, JSON_UNESCAPED_UNICODE));
                        $ordinary_error += 1;
                        $ordinary_error_msg .= "no={$ordinary_v['no']}, message={$pay_result['message']}; ";
                    } else {
                        $this->logger->info('普通付款支付成功, 业务单号=' . $ordinary_v['apply_no']);
                        $ordinary_success += 1;
                    }
                }
            }

            $log .= PHP_EOL . '报销单据处理结果: 成功 ' . $reimbursement_success . ' 条, 失败 ' . $reimbursement_error . ' 条' . PHP_EOL;
            $log .= '普通付款单据处理结果: 成功 ' . $ordinary_success . ' 条, 失败 ' . $ordinary_error . ' 条' . PHP_EOL . PHP_EOL;
            $log .= '报销支付失败的信息: ' . $reimbursement_error_msg . PHP_EOL;
            $log .= '普通付款支付失败的信息: ' . $ordinary_error_msg . PHP_EOL;

        } catch (Exception $e) {
            $message = $e->getMessage() . 'trace=' . $e->getTraceAsString();
            $log .= 'Exception message=' . $message . PHP_EOL;
            $this->logger->notice('自动取消超时待支付单据 Exception log : ' . $log);
        }

        $log .= '结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
        $this->logger->info('自动取消超时待支付单据 success log : ' . $log);
        exit($log);
    }


}
